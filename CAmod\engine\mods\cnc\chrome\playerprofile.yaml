Container@LOCAL_PROFILE_PANEL:
	Logic: LocalProfileLogic
	Width: 270
	Height: 100
	Children:
		Background@PROFILE_HEADER:
			Width: PARENT_WIDTH
			Height: 50
			Background: panel-black
			Children:
				Label@PROFILE_NAME:
					X: 10
					Y: 5
					Width: PARENT_WIDTH - 20
					Height: 25
					Font: MediumBold
				Label@PROFILE_RANK:
					X: 10
					Y: 24
					Width: PARENT_WIDTH - 20
					Height: 25
					Font: TinyBold
				Button@DESTROY_KEY:
					X: PARENT_WIDTH - 70
					Y: 15
					Width: 60
					Height: 20
					Font: TinyBold
					Text: button-profile-header-logout
		Background@BADGES_CONTAINER:
			Width: PARENT_WIDTH
			Y: 48
			Visible: false
			Background: panel-black
		Background@GENERATE_KEYS:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 6
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-generate-keys-desc-a
				Label@DESC_B:
					Y: 22
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-generate-keys-desc-b
				Label@DESC_C:
					Y: 38
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-generate-keys-desc-c
				Button@GENERATE_KEY:
					X: (PARENT_WIDTH - WIDTH) / 2
					Y: 70
					Width: 240
					Height: 20
					Font: TinyBold
					Text: button-generate-keys-key
		Background@GENERATING_KEYS:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 14
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-generating-keys-desc-a
				Label@DESC_B:
					Y: 30
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-generating-keys-desc-b
				ProgressBar:
					X: (PARENT_WIDTH - WIDTH) / 2
					Y: 70
					Width: 240
					Height: 20
					Indeterminate: true
		Background@REGISTER_FINGERPRINT:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 3
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-register-fingerprint-desc-a
				Label@DESC_B:
					Y: 19
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-register-fingerprint-desc-b
				Label@DESC_C:
					Y: 35
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-register-fingerprint-desc-c
				Button@DELETE_KEY:
					X: 15
					Y: 70
					Width: 70
					Height: 20
					Font: TinyBold
					Text: button-cancel
				Button@CHECK_KEY:
					X: 185
					Y: 70
					Width: 70
					Height: 20
					Font: TinyBold
					Text: button-continue
		Background@CHECKING_FINGERPRINT:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 14
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-checking-fingerprint-desc-a
				Label@DESC_B:
					Y: 30
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-checking-fingerprint-desc-b
				ProgressBar:
					X: (PARENT_WIDTH - WIDTH) / 2
					Y: 70
					Width: 240
					Height: 20
					Indeterminate: true
		Background@FINGERPRINT_NOT_FOUND:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 14
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-fingerprint-not-found-desc-a
				Label@DESC_B:
					Y: 30
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-fingerprint-not-found-desc-b
				Button@FINGERPRINT_NOT_FOUND_CONTINUE:
					X: 185
					Y: 70
					Width: 70
					Height: 20
					Font: TinyBold
					Text: button-back
		Background@CONNECTION_ERROR:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@DESC_A:
					Y: 14
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-connection-error-desc-a
				Label@DESC_B:
					Y: 30
					Width: PARENT_WIDTH
					Height: 25
					Font: Small
					Align: Center
					Text: label-connection-error-desc-b
				Button@CONNECTION_ERROR_RETRY:
					X: 185
					Y: 70
					Width: 70
					Height: 20
					Font: TinyBold
					Text: button-retry

Container@PLAYER_PROFILE_BADGES_INSERT:
	Logic: PlayerProfileBadgesLogic
	Width: PARENT_WIDTH
	Children:
		Container@BADGE_TEMPLATE:
			Width: PARENT_WIDTH
			Height: 25
			Children:
				Badge@ICON:
					X: 6
					Y: 1
					Width: 24
					Height: 24
				Label@LABEL:
					X: 36
					Y: 2
					Width: PARENT_WIDTH - 60
					Height: 24
					Font: Bold
