C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\lua51.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Server.exe
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Server.deps.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Server.runtimeconfig.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Server.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Server.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.csproj.CopyComplete
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\refint\OpenRA.Server.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\OpenRA.Server.genruntimeconfig.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Server\obj\Release\ref\OpenRA.Server.dll
