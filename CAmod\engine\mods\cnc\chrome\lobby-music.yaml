Container@LOBBY_MUSIC_BIN:
	Logic: MusicPlayerLogic
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		LogicTicker@SONG_WATCHER:
		Container@LABEL_CONTAINER:
			Y: 0 - 23
			Width: PARENT_WIDTH
			Children:
				Label@MUSIC:
					Width: 308
					Height: 25
					Text: label-music-title
					Align: Center
					Font: Bold
				Label@TITLE:
					X: 317
					Width: 230
					Height: 25
					Text: label-container-title
					Font: Bold
				Label@LENGTH:
					X: PARENT_WIDTH - 80
					Height: 25
					Width: 50
					Text: label-music-controls-length
					Font: Bold
					Align: Right
		Background@CONTROLS:
			Background: panel-transparent
			Width: 308
			Height: PARENT_HEIGHT
			Children:
				Label@MUTE_LABEL:
					X: 60
					Y: 10
					Width: 300
					Height: 20
					Font: Small
				Label@TITLE_LABEL:
					Y: 45
					Width: PARENT_WIDTH
					Height: 25
					Align: Center
					Font: Bold
				Label@TIME_LABEL:
					Y: 65
					Width: PARENT_WIDTH
					Height: 25
					Align: Center
				Container@BUTTONS:
					X: (PARENT_WIDTH - WIDTH) / 2
					Y: 100
					Width: 131
					Children:
						Button@BUTTON_PREV:
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PREV:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: prev
						Button@BUTTON_PLAY:
							X: 35
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PLAY:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: play
						Button@BUTTON_PAUSE:
							Visible: false
							X: 35
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PAUSE:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: pause
						Button@BUTTON_STOP:
							X: 70
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_STOP:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: stop
						Button@BUTTON_NEXT:
							X: 105
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_NEXT:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: next
				Checkbox@SHUFFLE:
					X: 25
					Y: 150
					Width: 85
					Height: 20
					Font: Regular
					Text: checkbox-music-controls-shuffle
				Checkbox@REPEAT:
					X: PARENT_WIDTH - 15 - WIDTH
					Y: 150
					Width: 70
					Height: 20
					Font: Regular
					Text: checkbox-music-controls-loop
				Label@VOLUME_LABEL:
					Y: 182
					Width: 65
					Height: 25
					Align: Right
					Text: label-music-controls-volume
				ExponentialSlider@MUSIC_SLIDER:
					X: 70
					Y: 186
					Width: PARENT_WIDTH - 80
					Height: 20
					Ticks: 7
		ScrollPanel@MUSIC_LIST:
			X: 307
			Width: PARENT_WIDTH - 307
			Height: PARENT_HEIGHT
			Children:
				ScrollItem@MUSIC_TEMPLATE:
					Width: PARENT_WIDTH - 27
					Height: 25
					X: 2
					Visible: false
					EnableChildMouseOver: True
					Children:
						LabelWithTooltip@TITLE:
							X: 10
							Width: PARENT_WIDTH - 50
							Height: 25
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
						Label@LENGTH:
							X: PARENT_WIDTH - 60
							Width: 50
							Height: 25
							Align: Right
		Container@NO_MUSIC_LABEL:
			X: 307
			Width: PARENT_WIDTH - 307
			Visible: false
			Children:
				Label@TITLE:
					Y: 75
					Width: PARENT_WIDTH - 24
					Height: 25
					Font: Bold
					Align: Center
					Text: label-no-music-title
				Label@DESCA:
					Y: 95
					Width: PARENT_WIDTH - 24
					Height: 25
					Align: Center
					Text: label-no-music-desc-a
				Label@DESCB:
					Y: 115
					Width: PARENT_WIDTH - 24
					Height: 25
					Align: Center
					Text: label-no-music-desc-b
