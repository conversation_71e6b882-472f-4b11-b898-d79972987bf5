Player:
	Mo<PERSON><PERSON><PERSON><PERSON>@Cabal:
		Name: bot-cabal.name
		Type: cabal
	ModularBot@Watson:
		Name: bot-watson.name
		Type: watson
	ModularBot@HAL9001:
		Name: bot-hal9001.name
		Type: hal9001
	GrantConditionOnBotOwner@cabal:
		Condition: enable-cabal-ai
		Bots: cabal
	GrantConditionOnBotOwner@watson:
		Condition: enable-watson-ai
		Bots: watson
	GrantConditionOnBotOwner@hal9001:
		Condition: enable-hal9001-ai
		Bots: hal9001
	SupportPowerBotModule:
		RequiresCondition: enable-cabal-ai || enable-watson-ai || enable-hal9001-ai
		Decisions:
			Airstrike:
				OrderName: AirstrikePowerInfoOrder
				MinimumAttractiveness: 2000
				Consideration@1:
					Against: Enemy
					Types: Vehicle, Infantry
					Attractiveness: 3
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@2:
					Against: Ally
					Types: Ground, Water
					Attractiveness: -20
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 2c0
			IonCannonPower:
				OrderName: IonCannonPowerInfoOrder
				MinimumAttractiveness: 1000
				FineScanRadius: 2
				Consideration@1:
					Against: Enemy
					Types: Air, Tank, Vehicle, Infantry, Water
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@2:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Ally
					Types: Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 3c0
			NukePower:
				OrderName: NukePowerInfoOrder
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
	HarvesterBotModule:
		RequiresCondition: enable-cabal-ai || enable-watson-ai || enable-hal9001-ai
		HarvesterTypes: harv
		RefineryTypes: proc
	BaseBuilderBotModule@cabal:
		RequiresCondition: enable-cabal-ai
		BuildingQueues: Building.Nod, Building.GDI
		DefenseQueues: Support.Nod, Support.GDI
		MinimumExcessPower: 30
		MaximumExcessPower: 150
		ExcessPowerIncrement: 30
		ExcessPowerIncreaseThreshold: 5
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: nuke, nuk2
		BarracksTypes: pyle, hand
		VehiclesFactoryTypes: weap, afld
		ProductionTypes: pyle, hand, weap, afld, hpad
		SiloTypes: silo
		DefenseTypes: gtwr,gun,atwr,obli,sam
		BuildingLimits:
			proc: 4
			pyle: 3
			hand: 3
			hq: 1
			weap: 3
			afld: 3
			hpad: 0
			eye: 1
			tmpl: 1
			fix: 1
			silo: 1
		BuildingFractions:
			proc: 20
			pyle: 5
			hand: 5
			hq: 4
			weap: 9
			afld: 9
			gtwr: 5
			gun: 5
			atwr: 9
			obli: 7
			sam: 7
			eye: 1
			tmpl: 1
			fix: 1
			hpad: 2
	BaseBuilderBotModule@watson:
		RequiresCondition: enable-watson-ai
		BuildingQueues: Building.Nod, Building.GDI
		DefenseQueues: Support.Nod, Support.GDI
		MinimumExcessPower: 30
		MaximumExcessPower: 150
		ExcessPowerIncrement: 30
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: nuke,nuk2
		BarracksTypes: pyle,hand
		VehiclesFactoryTypes: weap,afld
		ProductionTypes: pyle,hand,weap,afld,hpad
		SiloTypes: silo
		DefenseTypes: gtwr,gun,atwr,obli,sam
		BuildingLimits:
			proc: 4
			pyle: 3
			hand: 3
			hq: 1
			weap: 3
			afld: 3
			hpad: 2
			eye: 1
			tmpl: 1
			fix: 1
			silo: 1
		BuildingFractions:
			proc: 17
			pyle: 2
			hand: 2
			hq: 1
			weap: 5
			afld: 5
			hpad: 4
			gtwr: 5
			gun: 5
			atwr: 9
			obli: 7
			eye: 1
			tmpl: 1
			sam: 7
			fix: 1
	BaseBuilderBotModule@hal9001:
		RequiresCondition: enable-hal9001-ai
		BuildingQueues: Building.Nod, Building.GDI
		DefenseQueues: Support.Nod, Support.GDI
		MinimumExcessPower: 30
		MaximumExcessPower: 210
		ExcessPowerIncrement: 30
		ExcessPowerIncreaseThreshold: 4
		ConstructionYardTypes: fact
		RefineryTypes: proc
		PowerTypes: nuke,nuk2
		BarracksTypes: pyle,hand
		VehiclesFactoryTypes: weap,afld
		ProductionTypes: pyle,hand,weap,afld,hpad
		SiloTypes: silo
		DefenseTypes: gtwr,gun,atwr,obli,sam
		BuildingLimits:
			proc: 4
			pyle: 4
			hand: 4
			hq: 1
			weap: 4
			afld: 4
			hpad: 2
			eye: 1
			tmpl: 1
			fix: 1
			silo: 1
		BuildingFractions:
			proc: 17
			pyle: 7
			hand: 9
			hq: 1
			weap: 8
			afld: 6
			hpad: 4
			gtwr: 5
			gun: 5
			atwr: 9
			obli: 7
			eye: 1
			tmpl: 1
			sam: 7
			fix: 1
	BuildingRepairBotModule:
		RequiresCondition: enable-cabal-ai || enable-watson-ai || enable-hal9001-ai
	SquadManagerBotModule@cabal:
		RequiresCondition: enable-cabal-ai
		SquadSize: 15
		ExcludeFromSquadsTypes: harv, mcv, a10
		ConstructionYardTypes: fact
		AirUnitsTypes: heli, orca
		ProtectionTypes: fact, fact.gdi, fact.nod, nuke, nuk2, proc, silo, pyle, hand, afld, weap, hpad, hq, fix, eye, tmpl, gun, sam, obli, gtwr, atwr, mcv, harv, miss
		IgnoredEnemyTargetTypes: Air
	UnitBuilderBotModule@cabal:
		RequiresCondition: enable-cabal-ai
		UnitQueues: Vehicle.Nod, Vehicle.GDI, Infantry.Nod, Infantry.GDI, Aircraft.Nod, Aircraft.GDI
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e5: 15
			harv: 10
			bggy: 5
			bike: 40
			ltnk: 25
			ftnk: 10
			arty: 60
			stnk: 40
			jeep: 5
			mtnk: 20
			msam: 40
			htnk: 50
			heli: 5
			orca: 5
		UnitLimits:
			harv: 8
	McvManagerBotModule:
		RequiresCondition: enable-cabal-ai || enable-watson-ai || enable-hal9001-ai
		McvTypes: mcv
		ConstructionYardTypes: fact
		McvFactoryTypes: weap,afld
	SquadManagerBotModule@watson:
		RequiresCondition: enable-watson-ai
		SquadSize: 15
		ExcludeFromSquadsTypes: harv, mcv, a10
		ConstructionYardTypes: fact
		AirUnitsTypes: heli, orca
		ProtectionTypes: fact, fact.gdi, fact.nod, nuke, nuk2, proc, silo, pyle, hand, afld, weap, hpad, hq, fix, eye, tmpl, gun, sam, obli, gtwr, atwr, mcv, harv, miss
		IgnoredEnemyTargetTypes: Air
	UnitBuilderBotModule@watson:
		RequiresCondition: enable-watson-ai
		UnitQueues: Vehicle.Nod, Vehicle.GDI, Infantry.Nod, Infantry.GDI, Aircraft.Nod, Aircraft.GDI
		UnitsToBuild:
			e1: 65
			e2: 30
			e3: 40
			e4: 30
			e5: 30
			harv: 10
			bggy: 10
			ftnk: 10
			arty: 40
			bike: 10
			heli: 10
			ltnk: 40
			stnk: 40
			orca: 10
			msam: 50
			htnk: 50
			jeep: 20
			mtnk: 50
		UnitLimits:
			harv: 8
	SquadManagerBotModule@hal9001:
		RequiresCondition: enable-hal9001-ai
		SquadSize: 8
		ExcludeFromSquadsTypes: harv, mcv, a10
		ConstructionYardTypes: fact
		AirUnitsTypes: heli, orca
		ProtectionTypes: fact, fact.gdi, fact.nod, nuke, nuk2, proc, silo, pyle, hand, afld, weap, hpad, hq, fix, eye, tmpl, gun, sam, obli, gtwr, atwr, mcv, harv, miss
		IgnoredEnemyTargetTypes: Air
	UnitBuilderBotModule@hal9001:
		RequiresCondition: enable-hal9001-ai
		UnitQueues: Vehicle.Nod, Vehicle.GDI, Infantry.Nod, Infantry.GDI, Aircraft.Nod, Aircraft.GDI
		UnitsToBuild:
			e1: 65
			e2: 30
			e3: 40
			e4: 50
			e5: 50
			harv: 16
			bggy: 10
			bike: 10
			ltnk: 40
			arty: 20
			ftnk: 5
			stnk: 40
			mlrs: 5
			heli: 10
			jeep: 20
			apc: 10
			mtnk: 50
			msam: 50
			htnk: 50
			orca: 10
		UnitLimits:
			harv: 8
