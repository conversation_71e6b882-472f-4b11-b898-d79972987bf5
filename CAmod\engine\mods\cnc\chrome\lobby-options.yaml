Container@LOBBY_OPTIONS_BIN:
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		Label@TITLE:
			Y: 0 - 24
			Width: PARENT_WIDTH
			Height: 25
			Font: Bold
			Align: Center
			Text: label-lobby-options-bin-title
		ScrollPanel:
			Logic: LobbyOptionsLogic
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Children:
				Container@LOBBY_OPTIONS:
					Y: 10
					Width: PARENT_WIDTH - 24
					Children:
						Container@CHECKBOX_ROW_TEMPLATE:
							Width: PARENT_WIDTH
							Height: 30
							Children:
								Checkbox@A:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								Checkbox@B:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								Checkbox@C:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
						Container@DROPDOWN_ROW_TEMPLATE:
							Height: 60
							Width: PARENT_WIDTH
							Children:
								LabelForInput@A_DESC:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: A
								DropDownButton@A:
									X: 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								LabelForInput@B_DESC:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: B
								DropDownButton@B:
									X: PARENT_WIDTH / 3 + 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
								LabelForInput@C_DESC:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Height: 20
									Visible: False
									For: C
								DropDownButton@C:
									X: (PARENT_WIDTH / 3) * 2 + 10
									Width: PARENT_WIDTH / 3 - 20
									Y: 25
									Height: 25
									Font: Regular
									Visible: False
									TooltipContainer: TOOLTIP_CONTAINER
