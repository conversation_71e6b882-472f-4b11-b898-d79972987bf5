Container@REPLAYBROWSER_PANEL:
	Logic: ReplayBrowserLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 900
	Height: 540
	Children:
		Label@TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: label-replaybrowser-panel-title
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Container@FILTER_AND_MANAGE_CONTAINER:
					X: 15
					Y: 15
					Width: 285
					Height: PARENT_HEIGHT
					Children:
						Container@FILTERS:
							Width: PARENT_WIDTH
							Height: 320
							Children:
								Label@FILTERS_TITLE:
									X: 85
									Y: 0 - 9
									Width: PARENT_WIDTH - 85
									Height: 25
									Font: Bold
									Align: Center
									Text: label-filters-title
								Label@FLT_GAMETYPE_DESC:
									X: 0
									Y: 15
									Width: 80
									Height: 25
									Text: label-filters-flt-gametype-desc
									Align: Right
								DropDownButton@FLT_GAMETYPE_DROPDOWNBUTTON:
									X: 85
									Y: 15
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Label@FLT_DATE_DESC:
									X: 0
									Y: 45
									Width: 80
									Height: 25
									Text: label-filters-flt-date-desc
									Align: Right
								DropDownButton@FLT_DATE_DROPDOWNBUTTON:
									X: 85
									Y: 45
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Label@FLT_DURATION_DESC:
									X: 0
									Y: 75
									Width: 80
									Height: 25
									Text: label-filters-flt-duration-desc
									Align: Right
								DropDownButton@FLT_DURATION_DROPDOWNBUTTON:
									X: 85
									Y: 75
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Label@FLT_MAPNAME_DESC:
									X: 0
									Y: 105
									Width: 80
									Height: 25
									Text: label-filters-flt-mapname-desc
									Align: Right
								DropDownButton@FLT_MAPNAME_DROPDOWNBUTTON:
									X: 85
									Y: 105
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Label@FLT_PLAYER_DESC:
									X: 0
									Y: 135
									Width: 80
									Height: 25
									Text: label-filters-flt-player-desc
									Align: Right
								DropDownButton@FLT_PLAYER_DROPDOWNBUTTON:
									X: 85
									Y: 135
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-flt-player
								Label@FLT_OUTCOME_DESC:
									X: 0
									Y: 165
									Width: 80
									Height: 25
									Text: label-filters-flt-outcome-desc
									Align: Right
								DropDownButton@FLT_OUTCOME_DROPDOWNBUTTON:
									X: 85
									Y: 165
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Label@FLT_FACTION_DESC:
									X: 0
									Y: 195
									Width: 80
									Height: 25
									Text: label-filters-flt-faction-desc
									Align: Right
								DropDownButton@FLT_FACTION_DROPDOWNBUTTON:
									X: 85
									Y: 195
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: dropdownbutton-filters-any
								Button@FLT_RESET_BUTTON:
									X: 85
									Y: 235
									Width: PARENT_WIDTH - 85
									Height: 25
									Text: button-filters-flt-reset
									Font: Bold
						Container@MANAGEMENT:
							X: 85
							Y: 395
							Width: PARENT_WIDTH - 85
							Children:
								Label@MANAGE_TITLE:
									Y: 1
									Width: PARENT_WIDTH
									Height: 25
									Font: Bold
									Align: Center
									Text: label-management-manage-title
								Button@MNG_RENSEL_BUTTON:
									Y: 30
									Width: PARENT_WIDTH
									Height: 25
									Text: button-management-mng-rensel
									Font: Bold
									Key: F2
								Button@MNG_DELSEL_BUTTON:
									Y: 60
									Width: PARENT_WIDTH
									Height: 25
									Text: button-management-mng-delsel
									Font: Bold
									Key: Delete
								Button@MNG_DELALL_BUTTON:
									Y: 90
									Width: PARENT_WIDTH
									Height: 25
									Text: button-management-mng-delall
									Font: Bold
				Container@REPLAY_LIST_CONTAINER:
					X: 314
					Y: 15
					Width: 383
					Height: PARENT_HEIGHT - 45
					Children:
						Label@REPLAYBROWSER_LABEL_TITLE:
							Y: 0 - 9
							Width: PARENT_WIDTH
							Height: 25
							Text: label-replay-list-container-replaybrowser-title
							Align: Center
							Font: Bold
						ScrollPanel@REPLAY_LIST:
							X: 0
							Y: 15
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							CollapseHiddenChildren: True
							Children:
								ScrollItem@REPLAY_TEMPLATE:
									Width: PARENT_WIDTH - 27
									Height: 25
									X: 2
									Visible: false
									EnableChildMouseOver: True
									Children:
										LabelWithTooltip@TITLE:
											X: 10
											Width: PARENT_WIDTH - 20
											Height: 25
											TooltipContainer: TOOLTIP_CONTAINER
											TooltipTemplate: SIMPLE_TOOLTIP
				Container@MAP_PREVIEW_ROOT:
					X: PARENT_WIDTH - WIDTH - 15
					Y: 30
					Width: 174
					Height: 250
				Container@REPLAY_INFO:
					X: PARENT_WIDTH - WIDTH - 15
					Y: 230
					Width: 174
					Height: PARENT_HEIGHT - 240
					Children:
						Label@DURATION:
							Y: 21
							Width: PARENT_WIDTH
							Height: 15
							Font: Tiny
							Align: Center
						ScrollPanel@PLAYER_LIST:
							Y: 40
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT - 45
							IgnoreChildMouseOver: true
							Children:
								ScrollItem@HEADER:
									Background: scrollheader
									Width: PARENT_WIDTH - 27
									Height: 13
									X: 2
									Y: 0
									Visible: false
									Children:
										Label@LABEL:
											Font: TinyBold
											Width: PARENT_WIDTH
											Height: 13
											Align: Center
								ScrollItem@TEMPLATE:
									Width: PARENT_WIDTH - 27
									Height: 25
									X: 2
									Y: 0
									Visible: false
									Children:
										Image@FLAG:
											X: 4
											Y: 6
											Width: 32
											Height: 16
										Label@LABEL:
											X: 40
											Width: PARENT_WIDTH - 50
											Height: 25
											Shadow: True
										Label@NOFLAG_LABEL:
											X: 5
											Width: PARENT_WIDTH
											Height: 25
		Button@CANCEL_BUTTON:
			Key: escape
			X: 0
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-back
		Button@WATCH_BUTTON:
			Key: return
			X: PARENT_WIDTH - 140
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-replaybrowser-panel-watch
		TooltipContainer@TOOLTIP_CONTAINER:
