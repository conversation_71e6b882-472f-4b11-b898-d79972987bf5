C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\lua51.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\SDL2.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\libEGL.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\libGLESv2.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\soft_oal.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\freetype6.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Platforms.Default.deps.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Platforms.Default.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.Platforms.Default.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.csproj.CopyComplete
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\refint\OpenRA.Platforms.Default.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\OpenRA.Platforms.Default.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Platforms.Default\obj\Release\ref\OpenRA.Platforms.Default.dll
