^DamagingExplosion:
	Warhead@1Dam: SpreadDamage
		Spread: 426
		Damage: 4000
		Versus:
			None: 100
			Wood: 100
			Light: 100
			Heavy: 100
		DamageTypes: Prone50Percent, Trigger<PERSON>rone, ExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: poof
		ImpactSounds: xplos.aud
		ImpactActors: false
	Warhead@3Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep

^DamagingExplosionHE:
	Inherits: ^DamagingExplosion
	Warhead@1Dam: SpreadDamage
		Versus:
			None: 90
			Wood: 75
			Light: 60
			Heavy: 25
			Concrete: 25

FlametankExplode:
	Inherits: ^DamagingExplosion
	Warhead@1Dam: SpreadDamage
		Spread: 1c0
		Damage: 11500
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@2Eff: CreateEffect
		Explosions: big_napalm
		ImpactSounds: flamer2.aud
	Warhead@3Smu: LeaveSmudge
		SmudgeType: Scorch

HeliCrash:
	Inherits: ^DamagingExplosion
	Warhead@1Dam: SpreadDamage
		Damage: 10000

HeliExplode:
	Warhead@1Eff: CreateEffect
		Explosions: small_building
		ImpactSounds: xplos.aud

UnitExplode:
	Inherits: ^DamagingExplosionHE
	Warhead@1Dam: SpreadDamage
		Damage: 5000
		Versus:
			Wood: 74
			Heavy: 24
			Concrete: 24
	Warhead@2Eff: CreateEffect
		ImpactSounds: xplobig6.aud

UnitExplodeShip:
	Inherits: ^DamagingExplosionHE
	Warhead@1Dam: SpreadDamage
		Damage: 5000
		Versus:
			Wood: 74
			Heavy: 24
			Concrete: 24
	Warhead@2Eff: CreateEffect
		Explosions: building
		ImpactSounds: xplobig6.aud

UnitExplodeSmall:
	Inherits: ^DamagingExplosionHE
	Warhead@2Eff: CreateEffect
		Explosions: big_frag
		ImpactSounds: xplobig4.aud

UnitExplodeBig:
	Inherits: ^DamagingExplosionHE
	Warhead@2Eff: CreateEffect
		Explosions: big_frag
		ImpactSounds: xplobig6.aud

UnitExplodeMech:
	Inherits: ^DamagingExplosionHE
	Warhead@2Eff: CreateEffect
		Explosions: poof
		ImpactSounds: xplosml2.aud

UnitExplodeHarvEmpty:
	Inherits: ^DamagingExplosionHE
	Warhead@2Eff: CreateEffect
		Explosions: building
		ImpactSounds: xplos.aud

UnitExplodeStealthTank:
	Inherits: UnitExplodeBig
	Warhead@1Dam: SpreadDamage
		InvalidTargets: StealthTank

GrenadierExplode:
	Inherits: ^DamagingExplosionHE
	Warhead@1Dam: SpreadDamage
		Spread: 256
		Damage: 1000
		Versus:
			Wood: 70
			Heavy: 20
			Concrete: 20
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@2Eff: CreateEffect
		ImpactSounds: xplosml2.aud

BuildingExplode:
	Warhead@1Eff: CreateEffect
		Explosions: building, building_napalm, med_frag, poof, small_building
		Delay: 1
		ImpactActors: false
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		Delay: 1

Napalm.Crate:
	Inherits: ^DamagingExplosionHE
	Warhead@1Dam: SpreadDamage
		Spread: 170
		Damage: 5000
		Falloff: 1000, 368, 135, 50, 18, 7, 0
		Versus:
			Wood: 100
			Concrete: 100
		AffectsParent: true
		DamageTypes: Prone50Percent, TriggerProne, FireDeath
	Warhead@2Eff: CreateEffect
		Explosions: med_napalm
		ImpactSounds: flamer2.aud
	Warhead@3Smu: LeaveSmudge
		SmudgeType: Scorch

TiberiumExplosion:
	Inherits: ^DamagingExplosionHE
	Warhead@1Dam: SpreadDamage
		Spread: 9
		Damage: 1000
		Versus:
			Wood: 70
			Heavy: 20
			Concrete: 20
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@2Eff: CreateEffect
		Explosions: chemball
		ImpactSounds: xplosml2.aud
	-Warhead@3Smu:
	Warhead@4Res: CreateResource
		AddsResourceType: Tiberium
		Size: 1,1
