E1:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 100
	Tooltip:
		Name: actor-e1.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 10
		Prerequisites: barracks
		Queue: Infantry.GDI, Infantry.Nod
		Description: actor-e1.description
	Mobile:
		Speed: 54
	Health:
		HP: 5000
	AutoTarget:
		ScanRadius: 4
	Armament:
		Weapon: M16
	Encyclopedia:
		Description: actor-e1.encyclopedia
		Order: 0
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	WithInfantryBody:
		IdleSequences: idle1,idle2,idle3,idle4
		DefaultAttackSequence: shoot

E2:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 160
	Tooltip:
		Name: actor-e2.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Infantry.GDI
		Description: actor-e2.description
	Mobile:
		Speed: 68
	Health:
		HP: 5000
	AutoTarget:
		ScanRadius: 4
	Armament:
		Weapon: Grenade
		LocalOffset: 0,0,427
		FireDelay: 15
	Encyclopedia:
		Description: actor-e2.encyclopedia
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	TakeCover:
		ProneOffset: 300,0,-227
	WithInfantryBody:
		DefaultAttackSequence: throw
	FireWarheadsOnDeath:
		Weapon: GrenadierExplode
		EmptyWeapon: GrenadierExplode
		Chance: 50

E3:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-e3.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: barracks
		Queue: Infantry.GDI, Infantry.Nod
		Description: actor-e3.description
	Mobile:
		Speed: 39
	Health:
		HP: 4500
	AutoTarget:
		ScanRadius: 6
	Armament:
		Weapon: Rockets
		LocalOffset: 256,43,341
		FireDelay: 5
	Encyclopedia:
		Description: actor-e3.encyclopedia
		Order: 20
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	TakeCover:
		ProneOffset: 180,0,-200
	WithInfantryBody:
		DefaultAttackSequence: shoot

E4:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 200
	Tooltip:
		Name: actor-e4.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Infantry.Nod
		Description: actor-e4.description
	Mobile:
		Speed: 54
	Health:
		HP: 9000
	AutoTarget:
		ScanRadius: 4
	Armament:
		Weapon: Flamethrower
		LocalOffset: 341,0,256
		FireDelay: 3
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-e4.encyclopedia
		Order: 30
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	TakeCover:
		ProneOffset: 190,0,-198
	WithMuzzleOverlay:
	WithInfantryBody:
		DefaultAttackSequence: shoot

E5:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-e5.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 50
		Prerequisites: tmpl, ~techlevel.high
		Queue: Infantry.Nod
		Description: actor-e5.description
	Mobile:
		Speed: 54
		Locomotor: chem
	Health:
		HP: 9000
	AutoTarget:
		ScanRadius: 4
	Armament:
		Weapon: Chemspray
		LocalOffset: 341,0,256
		FireDelay: 3
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-e5.encyclopedia
		Order: 40
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	TakeCover:
		ProneOffset: 190,0,-190
	WithMuzzleOverlay:
	-DamagedByTerrain:
	WithInfantryBody:
		DefaultAttackSequence: shoot

E6:
	Inherits: ^Soldier
	Inherits@selection: ^SelectableSupportUnit
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-e6.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: barracks
		Queue: Infantry.GDI, Infantry.Nod
		Description: actor-e6.description
	Mobile:
		Speed: 46
	Health:
		HP: 3000
	Encyclopedia:
		Description: actor-e6.encyclopedia
		Order: 50
		Scale: 3
		Category: Infantry
		PreviewOwner: NodUnits
	Passenger:
		CustomPipType: yellow
	InstantlyRepairs:
	RepairsBridges:
	CaptureManager:
	Captures@SABOTAGE:
		CaptureTypes: building-sabotage
		SabotageThreshold: 55
		PlayerExperience: 10
	Captures@CAPTURES:
		CaptureTypes: building
		PlayerExperience: 10
	Captures@CATURESHUSK:
		CaptureTypes: husk
		PlayerExperience: 10
		ValidRelationships: Enemy, Neutral, Ally
	-AttackFrontal:

RMBO:
	Inherits: ^Soldier
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Inherits@DECORATIONS: ^InfantryExperienceHospitalHazmatOverrides
	Valued:
		Cost: 1500
	Tooltip:
		Name: actor-rmbo.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 50
		Prerequisites: eye, ~techlevel.high
		Queue: Infantry.GDI
		Description: actor-rmbo.description
	Mobile:
		Speed: 68
	Health:
		HP: 15000
	Passenger:
		CustomPipType: red
	RevealsShroud:
		Range: 6c0
	AutoTarget:
		ScanRadius: 8
	Demolition:
		DetonationDelay: 45
		Voice: Demolish
	Armament:
		Weapon: Sniper
	Encyclopedia:
		Description: actor-rmbo.encyclopedia
		Order: 60
		Scale: 3
		Category: Infantry
		PreviewOwner: GDI
	WithInfantryBody:
		DefaultAttackSequence: shoot
		IdleSequences: idle1,idle2,idle3
	ExternalCondition@PRODUCED:
		Condition: produced
	VoiceAnnouncement:
		RequiresCondition: produced
		Voice: Build
	AnnounceOnKill:
	Voiced:
		VoiceSet: CommandoVoice

PVICE:
	Inherits: ^Viceroid
	Buildable:
		Queue: Infantry.GDI, Infantry.Nod
		BuildPaletteOrder: 50
		Prerequisites: ~disabled
		Description: actor-pvice.description
	Tooltip:
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	ActorLostNotification:
		TextNotification: notification-unit-lost
	Encyclopedia:
		Description: actor-pvice.encyclopedia
		Order: 70
		Scale: 3
		Category: Civilian
		HideBuildable: true
		PreviewOwner: NodUnits

STEG:
	Inherits: ^DINO
	Tooltip:
		Name: actor-steg.name
	Armament:
		Weapon: tail
	WithDeathAnimation:
		DeathSequencePalette: terrain
		DeathPaletteIsPlayerPalette: false
	Buildable:
		Description: actor-steg.description

TREX:
	Inherits: ^DINO
	Tooltip:
		Name: actor-trex.name
	Armament:
		Weapon: teeth
	Selectable:
		Bounds: 2048, 1536, 85, 42
		DecorationBounds: 2218, 1621
	Buildable:
		Description: actor-trex.description

TRIC:
	Inherits: ^DINO
	Tooltip:
		Name: actor-tric.name
	Armament:
		Weapon: horn
	Buildable:
		Description: actor-tric.description
	Selectable:
		DecorationBounds: 1450, 1024, 0, 85

RAPT:
	Inherits: ^DINO
	Tooltip:
		Name: actor-rapt.name
	Armament:
		Weapon: claw
	Buildable:
		Description: actor-rapt.description
