Container@SERVER_LOBBY:
	Logic: LobbyLogic
		ChatTemplates:
			Chat: CHAT_LINE_TEMPLATE
			Join: SYSTEM_LINE_TEMPLATE
			Leave: SYSTEM_LINE_TEMPLATE
			System: SYSTEM_LINE_TEMPLATE
			Mission: CHAT_LINE_TEMPLATE
			Feedback: TRANSIENT_LINE_TEMPLATE
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 900
	Height: 540
	Children:
		Label@SERVER_NAME:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Container@MAP_PREVIEW_ROOT:
					X: PARENT_WIDTH - 15 - WIDTH
					Y: 30
					Width: 174
					Height: 250
				DropDownButton@SLOTS_DROPDOWNBUTTON:
					X: 15
					Y: 254
					Width: 211
					Height: 25
					Text: dropdownbutton-bg-slots
				But<PERSON>@RESET_OPTIONS_BUTTON:
					X: 15
					Y: 254
					Width: 211
					Height: 25
					Text: button-bg-reset-options
				Container@SKIRMISH_TABS:
					X: 697 - WIDTH
					Width: 465
					Visible: False
					Children:
						Button@PLAYERS_TAB:
							Y: 248
							Width: 151
							Height: 31
							Text: button-skirmish-tabs-players-tab
						Button@OPTIONS_TAB:
							X: 157
							Y: 248
							Width: 151
							Height: 31
							Text: button-skirmish-tabs-options-tab
						Button@MUSIC_TAB:
							X: 314
							Y: 248
							Width: 151
							Height: 31
							Text: label-music-title
				Container@MULTIPLAYER_TABS:
					X: 697 - WIDTH
					Width: 465
					Visible: False
					Children:
						Button@PLAYERS_TAB:
							Y: 248
							Width: 112
							Height: 31
							Text: button-multiplayer-tabs-players-tab
						Button@OPTIONS_TAB:
							X: 118
							Y: 248
							Width: 112
							Height: 31
							Text: button-multiplayer-tabs-options-tab
						Button@MUSIC_TAB:
							X: 236
							Y: 248
							Width: 112
							Height: 31
							Text: label-music-title
						Button@SERVERS_TAB:
							X: 354
							Y: 248
							Width: 111
							Height: 31
							Text: button-multiplayer-tabs-servers-tab
				Button@CHANGEMAP_BUTTON:
					X: PARENT_WIDTH - WIDTH - 15
					Y: 254
					Width: 174
					Height: 25
					Text: button-bg-changemap
				Container@TOP_PANELS_ROOT:
					X: 15
					Y: 30
					Width: 682
					Height: 219
				Container@LOBBYCHAT:
					X: 15
					Y: 285
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT - 300
					Children:
						ScrollPanel@CHAT_DISPLAY:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT - 30
							TopBottomSpacing: 3
							ItemSpacing: 2
						Button@CHAT_MODE:
							Y: PARENT_HEIGHT - HEIGHT
							Width: 50
							Height: 25
							Text: button-lobbychat-chat-mode.label
							Font: Bold
							Key: ToggleChatMode
							TooltipText: button-lobbychat-chat-mode.tooltip
							TooltipContainer: TOOLTIP_CONTAINER
						TextField@CHAT_TEXTFIELD:
							X: 55
							Y: PARENT_HEIGHT - HEIGHT
							Width: PARENT_WIDTH - 55
							Height: 25
		Button@DISCONNECT_BUTTON:
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-server-lobby-disconnect
		Button@START_GAME_BUTTON:
			X: PARENT_WIDTH - WIDTH
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-server-lobby-start-game
		Container@FACTION_DROPDOWN_PANEL_ROOT:
		TooltipContainer@TOOLTIP_CONTAINER:
