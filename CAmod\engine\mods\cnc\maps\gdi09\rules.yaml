World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, gdi09.lua, gdi09-AI.lua
	MusicPlaylist:
		StartingMusic: march
		VictoryMusic: gdi_win1
	MissionData:
		Briefing: Take out the Nod turrets along the shore so Gunboats can approach the Nod base.\n\nThe Nod base must be destroyed.\n\nIf Gunboats can get in, they should be able to destroy the base with ease.\n\nKeep an eye out for the new weapon rumored to be under development.
		BriefingVideo: gdi9.vqa
		WinVideo: gunboat.vqa
		LossVideo: gameover.vqa

ATWR:
	Buildable:
		Prerequisites: ~disabled

NUK2:
	Buildable:
		Prerequisites: ~disabled

HPAD:
	Buildable:
		Prerequisites: ~disabled

BRIK:
	Buildable:
		Prerequisites: ~disabled

EYE:
	Buildable:
		Prerequisites: ~disabled

GUN:
	Buildable:
		Prerequisites: ~disabled

OBLI:
	Buildable:
		Prerequisites: ~disabled

TMPL:
	Buildable:
		Prerequisites: ~disabled

HTNK:
	Buildable:
		Prerequisites: ~disabled

TRAN:
	Buildable:
		Prerequisites: ~disabled

ORCA:
	Buildable:
		Prerequisites: ~disabled

RMBO:
	Buildable:
		Prerequisites: ~disabled

MSAM:
	Buildable:
		Prerequisites: ~disabled

MCV:
	Buildable:
		Prerequisites: ~disabled

BOAT:
	Buildable:
		Prerequisites: ~disabled

FTNK:
	Buildable:
		Prerequisites: ~disabled

STNK:
	Buildable:
		Prerequisites: ~disabled

HELI:
	Buildable:
		Prerequisites: ~disabled

LTNK:
	Buildable:
		Prerequisites: ~afld

ARTY:
	Buildable:
		Prerequisites: ~afld

E4:
	Buildable:
		Prerequisites: barracks

E5:
	Buildable:
		Prerequisites: ~disabled

MLRS:
	Buildable:
		Prerequisites: ~disabled

SAM:
	Buildable:
		Prerequisites: ~disabled

^Bridge:
	DamageMultiplier@INVULNERABLE:
		Modifier: 0

BRIDGEHUT:
	-Targetable:

TRAN.Extraction:
	Inherits: TRAN
	RevealsShroud:
		Range: 0c0
	RejectsOrders:
	-Selectable:
	RenderSprites:
		Image: tran
	Interactable:

TRAN.Insertion:
	Inherits: TRAN.Extraction
	Cargo:
		MaxWeight: 0

HQ:
	Tooltip:
	-AirstrikePower:
	Buildable:
		Description: actor-hq-description

airstrike.proxy:
	AirstrikePower:
		SquadSize: 2
		SquadOffset: -1536, 1024, 0

BOAT:
	AutoTarget:
		InitialStance: AttackAnything
	RejectsOrders:
		Except: Attack
