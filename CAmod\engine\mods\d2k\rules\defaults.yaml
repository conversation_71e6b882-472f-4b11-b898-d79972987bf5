^ExistsInWorld:
	AppearsOnRadar:
	UpdatesPlayerStatistics:
	CombatDebugOverlay:
	GivesExperience:
		PlayerExperienceModifier: 1
	ScriptTriggers:
	RenderDebugState:

^SpriteActor:
	BodyOrientation:
	QuantizeFacingsFromSequence:
	RenderSprites:

^GainsExperience:
	GainsExperience:
		LevelUpNotification: LevelUp
		LevelUpTextNotification: notification-unit-promoted
		Conditions:
			200: rank-veteran
			400: rank-veteran
			800: rank-veteran
			1600: rank-veteran
		LevelUpImage: crate-effects
	GrantCondition@RANK-ELITE:
		RequiresCondition: rank-veteran >= 4
		Condition: rank-elite
	DamageMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 96
	DamageMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 92
	DamageMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 88
	DamageMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 80
	FirepowerMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 105
	FirepowerMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 110
	FirepowerMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 115
	FirepowerMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 125
	SpeedMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 105
	SpeedMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 110
	SpeedMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 115
	SpeedMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 125
	ReloadDelayMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 96
	ReloadDelayMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 92
	ReloadDelayMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 88
	ReloadDelayMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 80
	InaccuracyMultiplier@RANK-1:
		RequiresCondition: rank-veteran == 1
		Modifier: 90
	InaccuracyMultiplier@RANK-2:
		RequiresCondition: rank-veteran == 2
		Modifier: 80
	InaccuracyMultiplier@RANK-3:
		RequiresCondition: rank-veteran == 3
		Modifier: 70
	InaccuracyMultiplier@RANK-ELITE:
		RequiresCondition: rank-elite
		Modifier: 50
	ChangesHealth@ELITE:
		Step: 0
		PercentageStep: 4
		Delay: 125
		StartIfBelow: 100
		DamageCooldown: 125
		RequiresCondition: rank-elite
	WithDecoration@RANK-1:
		Image: rank
		Sequence: rank-veteran-1
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		RequiresCondition: rank-veteran == 1
	WithDecoration@RANK-2:
		Image: rank
		Sequence: rank-veteran-2
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		RequiresCondition: rank-veteran == 2
	WithDecoration@RANK-3:
		Image: rank
		Sequence: rank-veteran-3
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		RequiresCondition: rank-veteran == 3
	WithDecoration@RANK-ELITE:
		Image: rank
		Sequence: rank-elite
		Palette: effect
		Position: BottomRight
		Margin: 5, 6
		RequiresCondition: rank-elite

^AutoTargetGround:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything
		ValidTargets: Infantry, Vehicle, Defense
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Infantry, Vehicle, Structure, Defense
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@CREEPS:
		ValidTargets: Creep

^AutoTargetGroundAssaultMove:
	Inherits: ^AutoTargetGround
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	GrantConditionOnBotOwner@BOTOWNER:
		Condition: bot-owned
		Bots: omnius, vidious, gladius
	GrantCondition@IGNORECREEPS:
		Condition: ignore-creeps
		RequiresCondition: bot-owned && (attack-move || assault-move)
	AutoTargetPriority@CREEPS:
		RequiresCondition: !ignore-creeps
	AttackMove:
		AttackMoveCondition: attack-move
		AssaultMoveCondition: assault-move

^AutoTargetVehicleAssaultMove:
	Inherits: ^AutoTargetGroundAssaultMove
	AutoTargetPriority@VEHICLES:
		ValidTargets: Vehicle
		InvalidTargets: Infantry, Structure, Defense
		RequiresCondition: bot-owned
		Priority: 10

^AutoTargetAll:
	AutoTarget:
		AttackAnythingCondition: stance-attackanything
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything
		ValidTargets: Infantry, Vehicle, Air, Defense
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything
		ValidTargets: Infantry, Vehicle, Air, Structure, Defense
		InvalidTargets: NoAutoTarget
	AutoTargetPriority@CREEPS:
		ValidTargets: Creep

^AutoTargetAllAssaultMove:
	Inherits: ^AutoTargetAll
	AutoTargetPriority@DEFAULT:
		RequiresCondition: !stance-attackanything && !assault-move
	AutoTargetPriority@ATTACKANYTHING:
		RequiresCondition: stance-attackanything || assault-move
	GrantConditionOnBotOwner@BOTOWNER:
		Condition: bot-owned
		Bots: omnius, vidious, gladius
	GrantCondition@IGNORECREEPS:
		Condition: ignore-creeps
		RequiresCondition: bot-owned && (attack-move || assault-move)
	AutoTargetPriority@CREEPS:
		RequiresCondition: !ignore-creeps
	AttackMove:
		AttackMoveCondition: attack-move
		AssaultMoveCondition: assault-move

^PlayerHandicaps:
	HandicapFirepowerMultiplier:
	HandicapDamageMultiplier:
	HandicapProductionTimeMultiplier:

^CliffAvalanche:
	ExternalCondition@rockAvalanche:
		Condition: rock_avalanche
	KillsSelf@rockAvalanche:
		RequiresCondition: rock_avalanche
		DamageTypes: SmallExplosionDeath

^Vehicle:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	Inherits@avalancheKill: ^CliffAvalanche
	Tooltip:
		GenericName: meta-vehicle-generic-name
	Huntable:
	OwnerLostAction:
		Action: Kill
	Mobile:
		TurnSpeed: 20
		Locomotor: vehicle
		PauseOnCondition: notmobile
	Selectable:
		Bounds: 1024, 1024
	Targetable:
		TargetTypes: Ground, Vehicle, C4
	Passenger:
		CargoType: Vehicle
	AttackMove:
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: notification-unit-lost
	Repairable:
		RepairActors: repair_pad
	Guard:
		Voice: Guard
	Guardable:
	WithFacingSpriteBody:
	Demolishable:
	TemporaryOwnerManager:
	MustBeDestroyed:
	Voiced:
		VoiceSet: VehicleVoice
	AutoCarryable:
		CarriedCondition: notmobile
		ReservedCondition: carryall-reserved
		LockedCondition: notmobile
	WithDecoration@CARRYALL:
		Image: pips
		Margin: 7, 9
		Sequence: pickup-indicator
		RequiresCondition: carryall-reserved
	RevealOnFire:
	RevealOnDeath:
		Duration: 100
		Radius: 2c512
	HitShape:
		Type: Circle
			Radius: 16
	MapEditorData:
		Categories: Vehicle
	GrantConditionOnDamageState@HEAVY:
		Condition: heavy-damage
	SpeedMultiplier@HEAVYDAMAGE:
		RequiresCondition: heavy-damage
		Modifier: 75
	FloatingSpriteEmitter@SMOKE:
		RequiresCondition: heavy-damage
		Image: smoke3
		Lifetime: 15, 20
		Speed: 3
		Gravity: 50
		SpawnFrequency: 5, 10
		RandomFacing: true
		RandomRate: 4
		Offset: 0, 0, 200
		TurnRate: 3
		Duration: 500

^Tank:
	Inherits: ^Vehicle
	Mobile:
		Locomotor: tank

^Husk:
	Inherits@1: ^SpriteActor
	Interactable:
	Health:
		HP: 10000
	Armor:
		Type: light
	HiddenUnderFog:
		Type: CenterPosition
		AlwaysVisibleRelationships: None
	Tooltip:
		GenericName: meta-husk-generic-name
	ScriptTriggers:
	WithFacingSpriteBody:
	HitShape:
		Type: Circle
			Radius: 16
	MapEditorData:
		Categories: Husk

^VehicleHusk:
	Inherits: ^Husk
	Husk:
		AllowedTerrain: Sand, Rock, Transition, Concrete, Spice, SpiceSand, SpiceBlobs, Dune
		Locomotor: tank
	Targetable:
		TargetTypes: Ground, Vehicle, Husk
		RequiresForceFire: true
	WithColoredOverlay@husk:
		Color: 00000060
	FireWarheadsOnDeath:
		Weapon: UnitExplodeMed
		EmptyWeapon: UnitExplodeMed
	CaptureManager:
	Capturable:
		Types: husk
	WithIdleOverlay@Burns:
		Image: fire
		Sequence: 1
		IsDecoration: True
		RequiresCondition: decoration1 || decoration3
	FloatingSpriteEmitter@SMOKE:
		Image: smoke3
		Lifetime: 10, 20
		Speed: 7
		Gravity: 50
		SpawnFrequency: 2, 10
		RandomFacing: true
		RandomRate: 4
		Offset: 0, 0, 200
		TurnRate: 3
		Duration: 1500
		RequiresCondition: decoration2 || decoration3
	ChangesHealth:
		Step: -30
		StartIfBelow: 101
		Delay: 4
	GrantRandomCondition:
		Conditions: decoration1, decoration2, decoration3
	TransformOnCapture:
		ForceHealthPercentage: 20
	InfiltrateForTransform:
		Types: Husk
		ForceHealthPercentage: 20

^AircraftHusk:
	Inherits: ^Husk
	Tooltip:
		GenericName: meta-aircrafthusk-generic-name
	WithShadow:
	FallsToEarth:
		MaximumSpinSpeed: 0
		Moves: True
		Explosion: UnitExplodeLarge
	-MapEditorData:

^Infantry:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^GainsExperience
	Inherits@3: ^SpriteActor
	Inherits@selection: ^SelectableCombatUnit
	Inherits@handicaps: ^PlayerHandicaps
	Inherits@avalancheKill: ^CliffAvalanche
	Tooltip:
		GenericName: meta-infantry-generic-name
	Huntable:
	OwnerLostAction:
		Action: Kill
		DeathTypes: ExplosionDeath
	Health:
	Armor:
		Type: none
	RevealsShroud:
		Range: 3c768
	Mobile:
		AlwaysTurnInPlace: true
		Locomotor: foot
		TerrainCursors:
			Rough: move-rough
	Selectable:
		Bounds: 768, 768, 0, -128
		DecorationBounds: 384, 640, 0, -128
	Targetable:
		TargetTypes: Ground, Infantry
	QuantizeFacingsFromSequence:
		Sequence: stand
	WithInfantryBody:
		IdleSequences: idle1, idle2
	TakeCover:
		DamageModifiers:
			Prone50Percent: 50
		DamageTriggers: TriggerProne
		ProneOffset: 300,0,0
	WithDeathAnimation:
		DeathTypes:
			ExplosionDeath: 1
			SoundDeath: 2
			SmallExplosionDeath: 3
			BulletDeath: 4
		CrushedSequence: die-crushed
	AttackMove:
	Passenger:
		CargoType: Infantry
	HiddenUnderFog:
	ActorLostNotification:
		TextNotification: notification-unit-lost
	Crushable:
		CrushSound: CRUSH1.WAV
	Guard:
		Voice: Guard
	Guardable:
	DetectCloaked:
		Range: 1c768
	DeathSounds:
		DeathTypes: ExplosionDeath, SoundDeath, SmallExplosionDeath, BulletDeath
	MustBeDestroyed:
	TerrainModifiesDamage:
		TerrainModifier:
			Rough: 80
	Voiced:
		VoiceSet: InfantryVoice
	RevealOnFire:
	RevealOnDeath:
		Duration: 100
	HitShape:
		Type: Circle
			Radius: 16
	MapEditorData:
		Categories: Infantry
	AttackFrontal:
		FacingTolerance: 0

^Plane:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@handicaps: ^PlayerHandicaps
	Interactable:
	Tooltip:
		GenericName: meta-plane-generic-name
	OwnerLostAction:
		Action: Kill
	AppearsOnRadar:
		UseLocation: true
	HiddenUnderFog:
		Type: GroundPosition
	ActorLostNotification:
		TextNotification: notification-unit-lost
	AttackMove:
	WithFacingSpriteBody:
	WithShadow:
	HitShape:
		Type: Circle
			Radius: 16
	MapEditorData:
		Categories: Aircraft

^Building:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	Inherits@selection: ^SelectableBuilding
	Inherits@handicaps: ^PlayerHandicaps
	Tooltip:
		GenericName: meta-building-generic-name
	Huntable:
	OwnerLostAction:
		Action: Kill
	RevealsShroud:
	Targetable:
		TargetTypes: Ground, C4, Structure
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512
	D2kBuilding:
		Dimensions: 1,1
		Footprint: x
		TerrainTypes: Rock, Concrete
		BuildSounds: BUILD1.WAV
		ConcretePrerequisites: global-auto-concrete
	D2kActorPreviewPlaceBuildingPreview:
		RequiresPrerequisites: !global-auto-concrete
		PreviewAlpha: 0.65
	RequiresBuildableArea:
		AreaTypes: building
		Adjacent: 3
	GivesBuildableArea:
		AreaTypes: building
	CaptureManager:
	Capturable:
		RequiresCondition: !build-incomplete
		Types: building
	SoundOnDamageTransition:
		DamagedSounds: EXPLSML1.WAV
		DestroyedSounds: EXPLHG1.WAV
	FireWarheadsOnDeath:
		Type: Footprint
		Weapon: BuildingExplode
		EmptyWeapon: BuildingExplode
	RepairableBuilding:
		RepairStep: 500
		PlayerExperience: 5
		RepairingNotification: Repairing
	SpawnActorsOnSell:
		ActorTypes: light_inf
		GuaranteedActorTypes: light_inf
	MustBeDestroyed:
		RequiredForShortGame: true
	FrozenUnderFog:
	CaptureNotification:
		TextNotification: notification-enemy-building-captured
		LoseTextNotification: notification-one-of-our-buildings-has-been-captured
	ActorLostNotification:
		Notification: BuildingLost
		TextNotification: notification-building-lost
	ShakeOnDeath:
	Demolishable:
		Condition: being-demolished
	Sellable:
		RequiresCondition: !build-incomplete && !being-demolished
		SellSounds: BUILD1.WAV
		Notification: StructureSold
	Guardable:
		Range: 3c0
	FireProjectilesOnDeath:
		Weapons: Debris, Debris2, Debris3, Debris4
		Pieces: 2, 5
		Range: 1c512, 4c0
	WithSpriteBody:
	WithMakeAnimation:
		Condition: build-incomplete
	WithCrumbleOverlay:
		RequiresCondition: !build-incomplete
	RevealOnDeath:
		Duration: 100
		Radius: 4c768
	MapEditorData:
		Categories: Building
	CommandBarBlacklist:
	WithBuildingRepairDecoration:
		Image: allyrepair
		Sequence: repair
		Position: Center
		Palette: player
		IsPlayerPalette: True

^Defense:
	Inherits: ^Building
	Inherits@selection: ^SelectableCombatBuilding
	WithSpriteTurret:
		RequiresCondition: !build-incomplete
	AttackTurreted:
		PauseOnCondition: build-incomplete
	RenderRangeCircle:
	DetectCloaked:
		Range: 1c768
	-GivesBuildableArea:
	WithMakeAnimation:
		BodyNames: make
	-Capturable:
	WithSpriteBody:
		Name: make
		Sequence: invisible
	WithWallSpriteBody:
		RequiresCondition: !build-incomplete
	LineBuildNode:
		Types: turret
	FireProjectilesOnDeath:
		Weapons: Debris, Debris3
		Pieces: 2, 2
		Range: 2c0, 4c0
	MustBeDestroyed:
		RequiredForShortGame: false
	RevealOnFire:
	Targetable:
		TargetTypes: Ground, C4, Structure, Defense
	MapEditorData:
		Categories: Defense
	-CommandBarBlacklist:

^DisableOnLowPower:
	WithColoredOverlay@IDISABLE:
		RequiresCondition: disabled
		Color: 000000B4
	GrantConditionOnPowerState@LOWPOWER:
		Condition: lowpower
		ValidPowerStates: Low, Critical
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower
		Condition: disabled

^DisableOnLowPowerOrPowerDown:
	Inherits: ^DisableOnLowPower
	GrantCondition@IDISABLE:
		RequiresCondition: lowpower || powerdown
		Condition: disabled
	ToggleConditionOnOrder:
		DisabledSound: EnablePower
		EnabledSound: DisablePower
		Condition: powerdown
		OrderName: PowerDown
	WithDecoration@POWERDOWN:
		Image: poweroff
		Sequence: offline
		Palette: chrome
		RequiresCondition: powerdown
		Position: Center
		Offsets:
			repairing: 10, 0
	PowerMultiplier@POWERDOWN:
		RequiresCondition: powerdown
		Modifier: 0
	RepairableBuilding:
		RepairCondition: repairing
	WithBuildingRepairDecoration:
		Offsets:
			powerdown: -10, 0

^Selectable:
	Selectable:
	SelectionDecorations:
	WithSpriteControlGroupDecoration:
		Margin: -1, -1
	DrawLineToTarget:

^SelectableCombatUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 10
		PriorityModifiers: Ctrl

^SelectableSupportUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 8
		PriorityModifiers: Ctrl, Alt

^SelectableEconomicUnit:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 6
		PriorityModifiers: Ctrl, Alt

^SelectableCombatBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 4

^SelectableBuilding:
	Inherits@selectiondecorations: ^Selectable
	Selectable:
		Priority: 2

^PrimaryBuilding:
	PrimaryBuilding:
		PrimaryCondition: primary
		ProductionQueues: Building
		SelectionNotification: PrimaryBuildingSelected
		SelectionTextNotification: notification-primary-building-selected
	WithTextDecoration@primary:
		RequiresCondition: primary
		Position: Top
		Margin: 0, 5
		RequiresSelection: true
		Text: PRIMARY

^Upgradeable:
	GrantConditionOnPrerequisite@UPGRADEABLE:
		Condition: stardecoration
	WithDecoration@upgraded:
		RequiresCondition: stardecoration
		Position: TopRight
		Margin: 6, 8
		RequiresSelection: true
		Image: pips
		Sequence: tag-upgraded

^UndestroyableTile:
	Inherits@1: ^ExistsInWorld
	Inherits@2: ^SpriteActor
	FrozenUnderFog:
	WithSpriteBody:
	Building:
		TerrainTypes: Rock, Concrete, Cliff, Sand, Rock, Transition, Spice, SpiceSand, Dune,
	Health:
		HP: 30000
	Armor:
		Type: invulnerable
	HitShape:
		UseTargetableCellsOffsets: false
		Type: Rectangle
			TopLeft: -512, -512
			BottomRight: 512, 512
	MapEditorData:
		Categories: Tile actors
	Interactable:

^DestroyableTile:
	Inherits: ^UndestroyableTile
	Targetable:
		TargetTypes: Ground, Cliff
		RequiresForceFire: true
	Armor:
		Type: wall
	Health:
		HP: 30000
	OwnerLostAction:
		Action: ChangeOwner
		Owner: Neutral
	HitShape:
		UseTargetableCellsOffsets: true
	FireWarheadsOnDeath:
		Type: CenterPosition
		Weapon: cliffExplode
		EmptyWeapon: cliffExplode
	MapEditorData:
		Categories: Destroable Tiles
	Tooltip:
		GenericName: meta-destroyabletile.generic-name
		Name: meta-destroyabletile.name

^DestroyedTile:
	Inherits: ^UndestroyableTile
	CaptureManager:
	Building:
	OwnerLostAction:
		Action: ChangeOwner
		Owner: Neutral
	Tooltip:
		GenericName: meta-destroyedtile.generic-name
		Name: meta-destroyedtile.name
	CaptureManager:
	CapturableProgressBar:
	CapturableProgressBlink:
		Interval: 30
	Capturable:
		Types: cliff
	HitShape:
		UseTargetableCellsOffsets: true
		Type: Rectangle
			TopLeft: -1048, -1048
			BottomRight: 1024, 1054
	Targetable:
		TargetTypes: Ground, Cliff
		RequiresForceFire: true
	MapEditorData:
		Categories: Destroyed Tiles
