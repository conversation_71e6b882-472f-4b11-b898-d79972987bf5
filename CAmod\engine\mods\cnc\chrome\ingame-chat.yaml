Container@CHAT_PANEL:
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: WINDOW_HEIGHT - HEIGHT - 56
	Width: 550
	Height: 194
	Logic: IngameChatLogic
		OpenTeamChatKey: OpenTeamChat
		OpenGeneralChatKey: OpenGeneralChat
		Templates:
			Chat: CHAT_LINE_TEMPLATE
			System: SYSTEM_LINE_TEMPLATE
			Mission: CHAT_LINE_TEMPLATE
	Children:
		LogicKeyListener@OPEN_CHAT_KEY_LISTENER:
		Container@CHAT_OVERLAY:
			Width: PARENT_WIDTH - 24
			Height: PARENT_HEIGHT - 30
			Visible: false
			Children:
				TextNotificationsDisplay@CHAT_DISPLAY:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					DisplayDurationMs: 10000
					BottomSpacing: 3
		Container@CHAT_CHROME:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Children:
				WorldButton@CHAT_MODE:
					Logic: AddFactionSuffixLogic
					Y: PARENT_HEIGHT - HEIGHT
					Width: 50
					Height: 25
					Text: button-chat-chrome-mode.label
					Font: Bold
					Key: ToggleChatMode
					TooltipText: button-chat-chrome-mode.tooltip
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					TooltipContainer: TOOLTIP_CONTAINER
				TextField@CHAT_TEXTFIELD:
					Logic: AddFactionSuffixLogic
					X: 55
					Y: PARENT_HEIGHT - HEIGHT
					Width: 466
					Height: 25
				Button@CHAT_CLOSE:
					Logic: AddFactionSuffixLogic
					X: 526
					Y: PARENT_HEIGHT - HEIGHT
					Width: 24
					Height: 25
					Children:
						Image:
							ImageCollection: lobby-bits
							ImageName: kick
							X: 6
							Y: 8
				LogicKeyListener@KEY_LISTENER:
				ScrollPanel@CHAT_SCROLLPANEL:
					Logic: AddFactionSuffixLogic
					Y: PARENT_HEIGHT - HEIGHT - 30
					Width: 550
					Height: 164
					TopBottomSpacing: 3
					ItemSpacing: 4
					Align: Bottom
