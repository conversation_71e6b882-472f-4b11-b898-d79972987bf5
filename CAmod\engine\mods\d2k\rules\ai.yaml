Player:
	Mo<PERSON><PERSON><PERSON><PERSON>@Omnius:
		Name: bot-omnius.name
		Type: omnius
	ModularBot@Vidius:
		Name: bot-vidius.name
		Type: vidious
	ModularBot@Gladius:
		Name: bot-gladius.name
		Type: gladius
	GrantConditionOnBotOwner@omnius:
		Condition: enable-omnius-ai
		Bots: omnius
	GrantConditionOnBotOwner@vidious:
		Condition: enable-vidious-ai
		Bots: vidious
	GrantConditionOnBotOwner@gladius:
		Condition: enable-gladius-ai
		Bots: gladius
	ProvidesPrerequisite@autoConreteForBots:
		Prerequisite: global-auto-concrete
		RequiresCondition: enable-omnius-ai || enable-vidious-ai || enable-gladius-ai
	SupportPowerBotModule:
		RequiresCondition: enable-omnius-ai || enable-vidious-ai || enable-gladius-ai
		Decisions:
			Airstrike:
				OrderName: AirstrikePowerInfoOrder
				MinimumAttractiveness: 2000
				Consideration@1:
					Against: Enemy
					Types: Vehicle, Tank, Infantry
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@2:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Ally
					Types: Ground
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 4c0
			NukePower:
				OrderName: NukePowerInfoOrder
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			Fremen:
				OrderName: ProduceActorPower.Fremen
				Consideration@1:
					Against: Ally
	HarvesterBotModule:
		RequiresCondition: enable-omnius-ai || enable-vidious-ai || enable-gladius-ai
		HarvesterTypes: harvester
		RefineryTypes: refinery
	BaseBuilderBotModule@omnius:
		RequiresCondition: enable-omnius-ai
		BuildingQueues: Building, Upgrade
		MinimumExcessPower: 50
		MaximumExcessPower: 200
		ExcessPowerIncrement: 50
		ExcessPowerIncreaseThreshold: 4
		MaxBaseRadius: 40
		ConstructionYardTypes: construction_yard
		RefineryTypes: refinery
		PowerTypes: wind_trap
		VehiclesFactoryTypes: light_factory, heavy_factory, starport
		ProductionTypes: light_factory, heavy_factory, barracks, starport
		SiloTypes: silo
		DefenseTypes: medium_gun_turret,large_gun_turret
		BuildingLimits:
			barracks: 3
			refinery: 4
			outpost: 1
			high_tech_factory: 1
			light_factory: 2
			heavy_factory: 2
			starport: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingFractions:
			barracks: 1
			refinery: 20
			medium_gun_turret: 8
			outpost: 1
			high_tech_factory: 1
			large_gun_turret: 6
			light_factory: 1
			heavy_factory: 1
			starport: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingDelays:
			upgrade.conyard: 10000
			repair_pad: 12000
			outpost: 5000
			research_centre: 15000
			upgrade.barracks: 5000
			upgrade.light: 5000
			starport: 15000
			upgrade.heavy: 15000
			medium_gun_turret: 3000
	BaseBuilderBotModule@vidious:
		RequiresCondition: enable-vidious-ai
		BuildingQueues: Building, Upgrade
		MinimumExcessPower: 50
		MaximumExcessPower: 200
		ExcessPowerIncrement: 50
		ExcessPowerIncreaseThreshold: 4
		MaxBaseRadius: 40
		ConstructionYardTypes: construction_yard
		RefineryTypes: refinery
		PowerTypes: wind_trap
		VehiclesFactoryTypes: light_factory, heavy_factory, starport
		ProductionTypes: light_factory, heavy_factory, barracks, starport
		SiloTypes: silo
		DefenseTypes: medium_gun_turret,large_gun_turret
		BuildingLimits:
			barracks: 3
			refinery: 4
			outpost: 1
			high_tech_factory: 1
			light_factory: 2
			heavy_factory: 2
			starport: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingFractions:
			barracks: 1
			refinery: 20
			medium_gun_turret: 5
			outpost: 1
			high_tech_factory: 1
			large_gun_turret: 10
			light_factory: 1
			heavy_factory: 1
			starport: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingDelays:
			upgrade.conyard: 12000
			repair_pad: 10000
			outpost: 11000
			upgrade.barracks: 8000
			upgrade.heavy: 10000
			high_tech_factory: 13000
			starport: 20000
	BaseBuilderBotModule@gladius:
		RequiresCondition: enable-gladius-ai
		BuildingQueues: Building, Upgrade
		MinimumExcessPower: 50
		MaximumExcessPower: 200
		ExcessPowerIncrement: 50
		ExcessPowerIncreaseThreshold: 4
		MaxBaseRadius: 40
		ConstructionYardTypes: construction_yard
		RefineryTypes: refinery
		PowerTypes: wind_trap
		VehiclesFactoryTypes: light_factory, heavy_factory, starport
		ProductionTypes: light_factory, heavy_factory, barracks, starport
		SiloTypes: silo
		DefenseTypes: medium_gun_turret,large_gun_turret
		BuildingLimits:
			barracks: 3
			refinery: 4
			outpost: 1
			high_tech_factory: 1
			light_factory: 2
			heavy_factory: 2
			starport: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingFractions:
			barracks: 1
			refinery: 20
			medium_gun_turret: 4
			outpost: 1
			high_tech_factory: 1
			large_gun_turret: 12
			light_factory: 1
			heavy_factory: 1
			repair_pad: 1
			research_centre: 1
			palace: 1
			upgrade.conyard: 1
			upgrade.barracks: 1
			upgrade.light: 1
			upgrade.heavy: 1
			upgrade.hightech: 1
		BuildingDelays:
			upgrade.conyard: 10000
			repair_pad: 10000
			repair_pad.bot: 10000
			upgrade.barracks: 3500
			upgrade.heavy: 15000
			outpost: 12000
			starport: 15000
			upgrade.light: 10000
			medium_gun_turret: 5000
	BuildingRepairBotModule:
		RequiresCondition: enable-omnius-ai || enable-vidious-ai || enable-gladius-ai
	SquadManagerBotModule@omnius:
		RequiresCondition: enable-omnius-ai
		SquadSize: 8
		MaxBaseRadius: 40
		ExcludeFromSquadsTypes: harvester, mcv, carryall, carryall.reinforce, ornithopter
		ConstructionYardTypes: construction_yard
		IgnoredEnemyTargetTypes: Creep, Air, Cliff
		ProtectionTypes: mcv, harvester, construction_yard, conyard.atreides, conyard.harkonnen, conyard.ordos, wind_trap, barracks, refinery, silo, light_factory, heavy_factory, outpost, starport, medium_gun_turret, large_gun_turret, repair_pad, high_tech_factory, research_centre, palace, mcv.starport, harvester.starport
	UnitBuilderBotModule@omnius:
		RequiresCondition: enable-omnius-ai
		UnitQueues: Infantry, Vehicle, Armor, Starport, Aircraft
		UnitsToBuild:
			carryall: 1
			light_inf: 65
			trooper: 40
			mpsardaukar: 20
			grenadier: 20
			harvester: 1
			trike.starport: 5
			quad.starport: 7
			siege_tank.starport: 5
			missile_tank.starport: 7
			combat_tank_a.starport: 15
			combat_tank_h.starport: 15
			combat_tank_o.starport: 15
			sonic_tank: 10
			devastator: 10
			deviator: 7
			trike: 10
			raider: 10
			quad: 15
			siege_tank: 10
			missile_tank: 15
			stealth_raider: 5
			combat_tank_a: 100
			combat_tank_h: 100
			combat_tank_o: 100
		UnitLimits:
			harvester: 15
			carryall: 8
	McvManagerBotModule:
		RequiresCondition: enable-omnius-ai || enable-vidious-ai || enable-gladius-ai
		McvTypes: mcv
		ConstructionYardTypes: construction_yard
		McvFactoryTypes: heavy_factory, starport
	SquadManagerBotModule@vidious:
		RequiresCondition: enable-vidious-ai
		SquadSize: 6
		MaxBaseRadius: 40
		ExcludeFromSquadsTypes: harvester, mcv, carryall, carryall.reinforce, ornithopter
		ConstructionYardTypes: construction_yard
		IgnoredEnemyTargetTypes: Creep, Air, Cliff
		ProtectionTypes: mcv, harvester, construction_yard, conyard.atreides, conyard.harkonnen, conyard.ordos, wind_trap, barracks, refinery, silo, light_factory, heavy_factory, outpost, starport, medium_gun_turret, large_gun_turret, repair_pad, high_tech_factory, research_centre, palace, mcv.starport, harvester.starport
	UnitBuilderBotModule@vidious:
		RequiresCondition: enable-vidious-ai
		UnitQueues: Infantry, Vehicle, Armor, Starport, Aircraft
		UnitsToBuild:
			carryall: 1
			light_inf: 65
			trooper: 40
			mpsardaukar: 20
			grenadier: 20
			harvester: 1
			trike.starport: 7
			quad.starport: 12
			siege_tank.starport: 5
			missile_tank.starport: 7
			combat_tank_a.starport: 15
			combat_tank_h.starport: 15
			combat_tank_o.starport: 15
			sonic_tank: 50
			devastator: 40
			deviator: 5
			trike: 15
			raider: 15
			quad: 25
			siege_tank: 10
			missile_tank: 15
			stealth_raider: 5
			combat_tank_a: 100
			combat_tank_h: 100
			combat_tank_o: 100
		UnitLimits:
			harvester: 15
			carryall: 8
	SquadManagerBotModule@gladius:
		RequiresCondition: enable-gladius-ai
		SquadSize: 10
		MaxBaseRadius: 40
		ExcludeFromSquadsTypes: harvester, mcv, carryall, carryall.reinforce, ornithopter
		ConstructionYardTypes: construction_yard
		IgnoredEnemyTargetTypes: Creep, Air, Cliff
		ProtectionTypes: mcv, harvester, construction_yard, conyard.atreides, conyard.harkonnen, conyard.ordos, wind_trap, barracks, refinery, silo, light_factory, heavy_factory, outpost, starport, medium_gun_turret, large_gun_turret, repair_pad, high_tech_factory, research_centre, palace, mcv.starport, harvester.starport
	UnitBuilderBotModule@gladius:
		RequiresCondition: enable-gladius-ai
		UnitQueues: Infantry, Vehicle, Armor, Starport, Aircraft
		UnitsToBuild:
			carryall: 1
			light_inf: 65
			trooper: 40
			mpsardaukar: 20
			grenadier: 20
			harvester: 1
			trike.starport: 5
			quad.starport: 7
			siege_tank.starport: 5
			missile_tank.starport: 7
			combat_tank_a.starport: 15
			combat_tank_h.starport: 15
			combat_tank_o.starport: 15
			sonic_tank: 10
			devastator: 10
			deviator: 7
			trike: 10
			raider: 10
			quad: 15
			siege_tank: 10
			missile_tank: 15
			stealth_raider: 7
			combat_tank_a: 100
			combat_tank_h: 100
			combat_tank_o: 100
		UnitLimits:
			harvester: 15
			carryall: 8
