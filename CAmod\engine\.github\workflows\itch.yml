name: Deploy itch.io Packages

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Git Tag'
        required: true
        default: 'release-xxxxxxxx'

permissions: {}
jobs:
  itch:
    name: Deploy to itch.io
    runs-on: ubuntu-22.04
    if: github.repository == 'openra/openra'
    steps:
      - name: Download Butler
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -cq -O butler-linux-amd64.zip https://broth.itch.ovh/butler/linux-amd64/LATEST/archive/default
          unzip butler-linux-amd64.zip
          rm butler-linux-amd64.zip
          chmod +x butler
          ./butler -V
          ./butler login

      - name: Publish Windows Installer
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://github.com/${{ github.repository }}/releases/download/${{ github.event.inputs.tag }}/OpenRA-${{ github.event.inputs.tag }}-x64.exe"
          ./butler push "OpenRA-${{ github.event.inputs.tag }}-x64.exe" "openra/openra:win" --userversion ${{ github.event.inputs.tag }}

      - name: Publish Windows Itch Bundle
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://raw.githubusercontent.com/${{ github.repository }}/${{ github.event.inputs.tag }}/packaging/.itch.toml"
          zip -u "OpenRA-${{ github.event.inputs.tag }}-x64-win-itch.zip" .itch.toml
          ./butler push "OpenRA-${{ github.event.inputs.tag }}-x64-win-itch.zip" "openra/openra:itch" --userversion ${{ github.event.inputs.tag }}

      - name: Publish macOS Package
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://github.com/${{ github.repository }}/releases/download/${{ github.event.inputs.tag }}/OpenRA-${{ github.event.inputs.tag }}.dmg"
          ./butler push "OpenRA-${{ github.event.inputs.tag }}.dmg" "openra/openra:macos" --userversion ${{ github.event.inputs.tag }}

      - name: Publish RA AppImage
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://github.com/${{ github.repository }}/releases/download/${{ github.event.inputs.tag }}/OpenRA-Red-Alert-x86_64.AppImage"
          ./butler push "OpenRA-Red-Alert-x86_64.AppImage" "openra/openra:linux-ra" --userversion ${{ github.event.inputs.tag }}

      - name: Publish TD AppImage
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://github.com/${{ github.repository }}/releases/download/${{ github.event.inputs.tag }}/OpenRA-Tiberian-Dawn-x86_64.AppImage"
          ./butler push "OpenRA-Tiberian-Dawn-x86_64.AppImage" "openra/openra:linux-cnc" --userversion ${{ github.event.inputs.tag }}

      - name: Publish D2k AppImage
        env:
          BUTLER_API_KEY: ${{ secrets.BUTLER_CREDENTIALS }}
        run: |
          wget -q "https://github.com/${{ github.repository }}/releases/download/${{ github.event.inputs.tag }}/OpenRA-Dune-2000-x86_64.AppImage"
          ./butler push "OpenRA-Dune-2000-x86_64.AppImage" "openra/openra:linux-d2k" --userversion ${{ github.event.inputs.tag }}
