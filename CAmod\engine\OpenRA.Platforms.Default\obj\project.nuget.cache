{"version": 2, "dgSpecHash": "1eSeEEYKDZOyc8T9gCBZnjpD5A0yFSXh1FGucv2G5pvMgd105W0C6yFpahcSr9GKCBz2C4VIvvVjBDLeJ7umYg==", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\OpenRA.Platforms.Default.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\linguini.bundle\\0.8.1\\linguini.bundle.0.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\linguini.shared\\0.8.0\\linguini.shared.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\linguini.syntax\\0.8.0\\linguini.syntax.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\6.0.2\\microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.nat\\3.0.4\\mono.nat.3.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-eluant\\1.0.22\\openra-eluant.1.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-freetype6\\1.0.11\\openra-freetype6.1.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-openal-cs\\1.0.22\\openra-openal-cs.1.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-sdl2-cs\\1.0.42\\openra-sdl2-cs.1.0.42.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\roslynator.analyzers\\4.2.0\\roslynator.analyzers.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\roslynator.formatting.analyzers\\4.2.0\\roslynator.formatting.analyzers.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.4.2\\sharpziplib.1.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stylecop.analyzers\\1.2.0-beta.435\\stylecop.analyzers.1.2.0-beta.435.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stylecop.analyzers.unstable\\1.2.0.435\\stylecop.analyzers.unstable.1.2.0.435.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.1\\system.text.encodings.web.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.11\\system.text.json.6.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\6.0.0\\system.threading.channels.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512"], "logs": []}