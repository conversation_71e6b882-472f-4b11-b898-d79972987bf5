World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, nod08b.lua, nod08b-AI.lua
	MusicPlaylist:
		StartingMusic: linefire
		VictoryMusic: nod_win1
	MissionData:
		Briefing: Given our limited number of troops, you must make use of all available resources.\n\nFind the abandoned GDI base in the area and bring it back online. Once operational, use GDI's own weapons against them.\n\nEnsure that no GDI forces survive.
		BackgroundVideo: tiberfx.vqa
		BriefingVideo: nod8.vqa
		LossVideo: flag.vqa
	SmudgeLayer@SCORCH:
		InitialSmudges:
			44,57: sc2,0
			40,56: sc1,0
			50,55: sc5,0
			45,55: sc3,0
			61,43: sc2,0
			58,42: sc6,0
			61,41: sc4,0
			55,41: sc1,0
			59,39: sc5,0
			53,39: sc5,0
			60,9: sc4,0
			58,9: sc6,0
			57,8: sc2,0
			54,6: sc3,0
			60,5: sc2,0
			55,5: sc4,0
			61,4: sc6,0
			55,4: sc6,0
			57,3: sc1,0
	<PERSON><PERSON><PERSON><PERSON><PERSON>er@CRATER:
		InitialSmudges:
			42,56: cr1,0
			49,55: cr1,0
			51,52: cr1,0
			41,52: cr1,0
			60,43: cr1,0
			59,41: cr1,0
			55,40: cr1,0
			49,40: cr1,0
			53,36: cr1,0
			55,6: cr1,0
			54,5: cr1,0

Player:
	PlayerResources:
		DefaultCash: 0

CYCL:
	Buildable:
		Prerequisites: ~disabled

NUK2:
	Buildable:
		Prerequisites: ~disabled

HPAD:
	Buildable:
		Prerequisites: ~disabled

BRIK:
	Buildable:
		Prerequisites: ~disabled

EYE:
	Buildable:
		Prerequisites: ~disabled

GUN:
	Buildable:
		Prerequisites: ~disabled

OBLI:
	Buildable:
		Prerequisites: ~disabled

TMPL:
	Buildable:
		Prerequisites: ~disabled

E2:
	Buildable:
		Prerequisites: ~pyle

E4:
	Buildable:
		Prerequisites: ~hand

E5:
	Buildable:
		Prerequisites: ~disabled

E6:
	-RepairsBridges:

HARV:
	Harvester:
		SearchFromProcRadius: 30
		SearchFromHarvesterRadius: 30

HTNK:
	Buildable:
		Prerequisites: ~disabled

HQ:
	AirstrikePower:
		Prerequisites: gdi
		SquadSize: 1

RMBO:
	Buildable:
		Prerequisites: ~disabled

MCV:
	Buildable:
		Prerequisites: ~disabled

FTNK:
	Buildable:
		Prerequisites: ~disabled

MLRS:
	Buildable:
		Prerequisites: ~disabled

MSAM:
	Buildable:
		Prerequisites: ~disabled

ATWR:
	Buildable:
		Prerequisites: ~disabled

HELI:
	Buildable:
		Prerequisites: ~disabled

SBAG:
	Buildable:
		Queue: Support.GDI, Support.Nod

GTWR:
	Buildable:
		Queue: Support.GDI

A10.IN:
	Inherits: A10
	RenderSprites:
		Image: A10
	Armament@BOMBS:
		Weapon: Napalm.in

airstrike.proxy:
	AirstrikePower:
		UnitType: a10.in

BOAT:
	Health:
		HP: 150000
	AutoTarget:
		InitialStance: AttackAnything
	RejectsOrders:
		Except: Attack
	RevealsShroud:
		Range: 4c0
		ValidRelationships: Enemy, Neutral, Ally

FACT.IN:
	Inherits: FACT
	RenderSprites:
		Image: FACT
	ProvidesPrerequisite:
		Prerequisite: fact
	CustomSellValue:
		Value: 12825

FACTOUT.IN:
	Inherits: FACT
	RenderSprites:
		Image: fact
	ProvidesPrerequisite:
		Prerequisite: fact
	Capturable:
		Types: building

NUKEOUT.IN:
	Inherits: NUKE
	RenderSprites:
		Image: nuke
	Buildable:
		Prerequisites: ~disabled
	ProvidesPrerequisite:
		Prerequisite: anypower
	Capturable:
		Types: building

PROCOUT.IN:
	Inherits: PROC
	RenderSprites:
		Image: proc
	Buildable:
		Prerequisites: ~disabled
	ProvidesPrerequisite:
		Prerequisite: proc
	Capturable:
		Types: building

TRAN.IN:
	Inherits: TRAN
	RejectsOrders:
	-Selectable:
	RenderSprites:
		Image: TRAN
	Buildable:
		Prerequisites: ~disabled
	Interactable:
