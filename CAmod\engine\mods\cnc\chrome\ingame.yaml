Container@INGAME_ROOT:
	Logic: LoadIngamePlayerOrObserverUILogic
	Children:
		LogicKeyListener@GLOBAL_KEYHANDLER:
			Logic: MusicHotkeyLogic, ScreenshotHotkeyLogic, MuteHotkeyLogic
				StopMusicKey: StopMusic
				PauseMusicKey: PauseMusic
				PrevMusicKey: PrevMusic
				NextMusicKey: NextMusic
				TakeScreenshotKey: TakeScreenshot
				MuteAudioKey: ToggleMute
		LogicKeyListener@WORLD_KEYHANDLER:
			Logic: CycleBasesHotkeyLogic, CycleProductionActorsHotkeyLogic, CycleHarvestersHotkeyLogic, JumpToLastEventHotkeyLogic, JumpToSelectedActorsHotkeyLogic, Reset<PERSON><PERSON>HotkeyLogic, TogglePlayerStanceColorHotkeyLogic, CycleStatusBarsHotkeyLogic, PauseHotkeyLogic, SelectUnitsByTypeHotkeyLogic, SelectAllUnitsHotkeyLogic
				CycleBasesKey: CycleBase
				CycleProductionActorsKey: CycleProductionBuildings
				CycleHarvestersKey: CycleHarvesters
				JumpToLastEventKey: ToLastEvent
				JumpToSelectedActorsKey: ToSelection
				ResetZoomKey: ResetZoom
				TogglePlayerStanceColorKey: TogglePlayerStanceColor
				CycleStatusBarsKey: CycleStatusBars
				PauseKey: Pause
				SelectAllUnitsKey: SelectAllUnits
				SelectUnitsByTypeKey: SelectUnitsByType
		Container@WORLD_ROOT:
			Children:
				LogicTicker@DISCONNECT_WATCHER:
					Logic: DisconnectWatcherLogic
				Label@MISSION_TEXT:
					X: WINDOW_WIDTH / 2 - 256
					Y: 22
					Width: 512
					Height: 25
					Font: Bold
					Align: Center
					Contrast: true
				StrategicProgress@STRATEGIC_PROGRESS:
					X: WINDOW_WIDTH / 2
					Y: 40
				WorldInteractionController@INTERACTION_CONTROLLER:
					Width: WINDOW_WIDTH
					Height: WINDOW_HEIGHT
				Container@PLAYER_ROOT:
		Container@MENU_ROOT:
		TooltipContainer@TOOLTIP_CONTAINER:
		MouseAttachment@MOUSE_ATTATCHMENT:

Container@PERF_WIDGETS:
	Logic: PerfDebugLogic
	Children:
		Label@PERF_TEXT:
			X: WINDOW_WIDTH - 200
			Y: WINDOW_HEIGHT - 90
			Width: 170
			Height: 40
			Contrast: true
			VAlign: Top
		Background@GRAPH_BG:
			Logic: AddFactionSuffixLogic
			X: 5
			Y: WINDOW_HEIGHT - HEIGHT - 156
			Width: 220
			Height: 220
			Background: panel-black
			Children:
				PerfGraph@GRAPH:
					X: 10
					Y: 10
					Width: 200
					Height: 200

Container@OBSERVER_WIDGETS:
	Logic: MenuButtonsChromeLogic, LoadIngamePerfLogic, LoadIngameChatLogic
	Children:
		Container@CHAT_ROOT:
		Container@PERF_ROOT:
		Container@HPF_ROOT:
			Logic: LoadIngameHierarchicalPathFinderOverlayLogic
			X: WINDOW_WIDTH - WIDTH - 270
			Y: 40
			Width: 175
		ViewportController:
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
			TooltipContainer: TOOLTIP_CONTAINER
			ZoomInKey: ZoomIn
			ZoomOutKey: ZoomOut
			ScrollUpKey: MapScrollUp
			ScrollDownKey: MapScrollDown
			ScrollLeftKey: MapScrollLeft
			ScrollRightKey: MapScrollRight
			JumpToTopEdgeKey: MapJumpToTopEdge
			JumpToBottomEdgeKey: MapJumpToBottomEdge
			JumpToLeftEdgeKey: MapJumpToLeftEdge
			JumpToRightEdgeKey: MapJumpToRightEdge
			BookmarkSaveKeyPrefix: MapBookmarkSave
			BookmarkRestoreKeyPrefix: MapBookmarkRestore
			BookmarkKeyCount: 4
		Container@GAME_TIMER_BLOCK:
			Logic: GameTimerLogic
			X: WINDOW_WIDTH / 2 - WIDTH
			Width: 100
			Height: 55
			Children:
				LabelWithTooltip@GAME_TIMER:
					Width: PARENT_WIDTH
					Height: 30
					Align: Center
					Font: Title
					Contrast: true
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: SIMPLE_TOOLTIP
				Label@GAME_TIMER_STATUS:
					Y: 35
					Width: PARENT_WIDTH
					Height: 15
					Align: Center
					Font: Bold
					Contrast: true
		Container@MUTE_INDICATOR:
			Logic: MuteIndicatorLogic
			X: WINDOW_WIDTH - WIDTH - 295
			Y: 5
			Width: 200
			Height: 25
			Children:
				Image@ICON:
					X: PARENT_WIDTH - WIDTH
					Y: 1
					Width: 24
					Height: 24
					ImageCollection: sidebar-bits
					ImageName: indicator-muted
				Label@LABEL:
					Width: PARENT_WIDTH - 30
					Height: 25
					Align: Right
					Text: label-mute-indicator
					Contrast: true
		LogicKeyListener@OBSERVER_KEY_LISTENER:
		MenuButton@OPTIONS_BUTTON:
			Key: escape
			X: WINDOW_WIDTH - 260 - WIDTH
			Y: 5
			Width: 30
			Height: 25
			TooltipText: button-observer-widget-options-tooltip
			TooltipContainer: TOOLTIP_CONTAINER
			DisableWorldSounds: true
			Children:
				Image:
					X: 7
					Y: 5
					ImageCollection: order-icons
					ImageName: options
		Background@RADAR:
			X: WINDOW_WIDTH - WIDTH - 5
			Y: 5
			Width: 256
			Height: 256
			Background: panel-gray
			Children:
				Radar:
					X: 1
					Y: 1
					Width: PARENT_WIDTH - 2
					Height: PARENT_HEIGHT - 2
				VideoPlayer@PLAYER:
					X: 1
					Y: 1
					Width: PARENT_WIDTH - 2
					Height: PARENT_HEIGHT - 2
					Skippable: false
		Background@REPLAY_PLAYER:
			Logic: ReplayControlBarLogic
			X: WINDOW_WIDTH - WIDTH - 5
			Y: 283
			Width: 256
			Height: 46
			Background: panel-black
			Visible: false
			Children:
				Button@BUTTON_PAUSE:
					X: 16
					Y: 10
					Width: 26
					Height: 26
					Key: Pause
					TooltipText: button-replay-player-pause-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					IgnoreChildMouseOver: true
					Children:
						Image@IMAGE_PAUSE:
							X: 5
							Y: 5
							Width: 16
							Height: 16
							ImageCollection: music
							ImageName: pause
				Button@BUTTON_PLAY:
					X: 16
					Y: 10
					Width: 26
					Height: 26
					Key: Pause
					TooltipText: button-replay-player-play-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					IgnoreChildMouseOver: true
					Children:
						Image@IMAGE_PLAY:
							X: 5
							Y: 5
							Width: 16
							Height: 16
							ImageCollection: music
							ImageName: play
				Button@BUTTON_SLOW:
					X: 57
					Y: 13
					Width: 36
					Height: 20
					Key: ReplaySpeedSlow
					TooltipText: button-replay-player-slow.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-replay-player-slow.label
					Font: TinyBold
				Button@BUTTON_REGULAR:
					X: 57 + 48
					Y: 13
					Width: 38
					Height: 20
					Key: ReplaySpeedRegular
					TooltipText: button-replay-player-regular.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-replay-player-regular.label
					Font: TinyBold
				Button@BUTTON_FAST:
					X: 57 + 48 * 2
					Y: 13
					Width: 38
					Height: 20
					Key: ReplaySpeedFast
					TooltipText: button-replay-player-fast.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-replay-player-fast.label
					Font: TinyBold
				Button@BUTTON_MAXIMUM:
					X: 57 + 48 * 3
					Y: 13
					Width: 38
					Height: 20
					Key: ReplaySpeedMax
					TooltipText: button-replay-player-maximum.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					VisualHeight: 0
					Text: button-replay-player-maximum.label
					Font: TinyBold
		DropDownButton@SHROUD_SELECTOR:
			Logic: ObserverShroudSelectorLogic
				CombinedViewKey: ObserverCombinedView
				WorldViewKey: ObserverWorldView
			X: WINDOW_WIDTH - WIDTH - 5
			Y: 260
			Width: 256
			Height: 25
			Font: Bold
			Children:
				LogicKeyListener@SHROUD_KEYHANDLER:
				Image@FLAG:
					X: 4
					Y: 4
					Width: 32
					Height: 16
				Label@LABEL:
					X: 40
					Width: PARENT_WIDTH
					Height: 25
					Shadow: True
				Label@NOFLAG_LABEL:
					X: 5
					Width: PARENT_WIDTH
					Height: 25
					Shadow: True
		Container@INGAME_OBSERVERSTATS_BG:
			Logic: ObserverStatsLogic
				StatisticsNoneKey: StatisticsNone
				StatisticsBasicKey: StatisticsBasic
				StatisticsEconomyKey: StatisticsEconomy
				StatisticsProductionKey: StatisticsProduction
				StatisticsSupportPowersKey: StatisticsSupportPowers
				StatisticsCombatKey: StatisticsCombat
				StatisticsArmyKey: StatisticsArmy
				StatisticsGraphKey: StatisticsGraph
				StatisticsArmyGraphKey: StatisticsArmyGraph
			X: 5
			Y: 5
			Width: 730
			Height: 240
			Children:
				DropDownButton@STATS_DROPDOWN:
					X: 0
					Y: 0
					Width: 185
					Height: 25
					Font: Bold
					Children:
						LogicKeyListener@STATS_DROPDOWN_KEYHANDLER:
				Container@GRAPH_BG:
					Y: 30
					X: 0
					Width: PARENT_WIDTH
					Height: 24
					Children:
						Container@BASIC_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 705
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-player-header
									Align: Left
									Shadow: True
								Label@CASH_HEADER:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-cash-header
									Align: Right
									Shadow: True
								Label@POWER_HEADER:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-power-header
									Align: Center
									Shadow: True
								Label@KILLS_HEADER:
									X: 320
									Y: 0
									Width: 40
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-kills-header
									Align: Right
									Shadow: True
								Label@DEATHS_HEADER:
									X: 360
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-deaths-header
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 420
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-assets-destroyed-header
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 500
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-assets-lost-header
									Align: Right
									Shadow: True
								Label@EXPERIENCE_HEADER:
									X: 580
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-experience-header
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN_HEADER:
									X: 640
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-basic-stats-actions-min-header
									Align: Right
									Shadow: True
						Container@ECONOMY_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 735
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-player-header
									Shadow: True
								Label@CASH_HEADER:
									X: 160
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-cash-header
									Align: Right
									Shadow: True
								Label@INCOME_HEADER:
									X: 240
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-income-header
									Align: Right
									Shadow: True
								Label@ASSETS_HEADER:
									X: 320
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-assets-header
									Align: Right
									Shadow: True
								Label@EARNED_HEADER:
									X: 400
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-earned-header
									Align: Right
									Shadow: True
								Label@SPENT_HEADER:
									X: 480
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-spent-header
									Align: Right
									Shadow: True
								Label@HARVESTERS_HEADER:
									X: 560
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-harvesters-header
									Align: Right
									Shadow: True
								Label@DERRICKS_HEADER:
									X: 645
									Width: 80
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-economy-stats-derricks-header
									Align: Right
									Shadow: True
						Container@PRODUCTION_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-production-stats-player-header
									Align: Left
									Shadow: True
								Label@PRODUCTION_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-production-stats-header
									Shadow: True
						Container@SUPPORT_POWERS_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-support-powers-player-header
									Align: Left
									Shadow: True
								Label@SUPPORT_POWERS_HEADER:
									X: 160
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-support-powers-header
									Shadow: True
						Container@ARMY_HEADERS:
							X: 0
							Y: 0
							Width: 400
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-army-player-header
									Align: Left
									Shadow: True
								Label@ARMY_HEADER:
									X: 160
									Y: 0
									Width: 100
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-army-header
									Shadow: True
						Container@COMBAT_STATS_HEADERS:
							X: 0
							Y: 0
							Width: 760
							Height: PARENT_HEIGHT
							Children:
								ColorBlock@HEADER_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@HEADER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@PLAYER_HEADER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-player-header
									Align: Left
									Shadow: True
								Label@ASSETS_DESTROYED_HEADER:
									X: 160
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-assets-destroyed-header
									Align: Right
									Shadow: True
								Label@ASSETS_LOST_HEADER:
									X: 235
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-assets-lost-header
									Align: Right
									Shadow: True
								Label@UNITS_KILLED_HEADER:
									X: 310
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-units-killed-header
									Align: Right
									Shadow: True
								Label@UNITS_DEAD_HEADER:
									X: 385
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-units-dead-header
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED_HEADER:
									X: 460
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-buildings-killed-header
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD_HEADER:
									X: 535
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-buildings-dead-header
									Align: Right
									Shadow: True
								Label@ARMY_VALUE_HEADER:
									X: 610
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-army-value-header
									Align: Right
									Shadow: True
								Label@VISION_HEADER:
									X: 700
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Font: Bold
									Text: label-combat-stats-vision-header
									Align: Right
									Shadow: True
				ScrollPanel@PLAYER_STATS_PANEL:
					X: 0
					Y: 54
					Width: PARENT_WIDTH
					Height: 240
					TopBottomSpacing: 0
					BorderWidth: 0
					Background:
					ScrollbarWidth: 24
					ScrollBar: Hidden
					Children:
						ScrollItem@TEAM_TEMPLATE:
							X: 0
							Y: 0
							Width: 650 #PARENT_WIDTH - 35
							Height: 24
							Children:
								ColorBlock@TEAM_COLOR:
									X: 0
									Y: 0
									Color: 00000090
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@TEAM_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									TopLeftColor: 00000090
									BottomLeftColor: 00000090
									Width: 200
									Height: PARENT_HEIGHT
								Label@TEAM:
									X: 10
									Y: 0
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
						ScrollItem@BASIC_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 705
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@POWER:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Center
									Shadow: True
								Label@KILLS:
									X: 320
									Y: 0
									Width: 40
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@DEATHS:
									X: 360
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 420
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 500
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@EXPERIENCE:
									X: 580
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ACTIONS_MIN:
									X: 640
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
						ScrollItem@ECONOMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 730
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@CASH:
									X: 160
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@INCOME:
									X: 240
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS:
									X: 320
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@EARNED:
									X: 400
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@SPENT:
									X: 480
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@HARVESTERS:
									X: 560
									Y: 0
									Width: 80
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@DERRICKS:
									X: 650
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
						ScrollItem@PRODUCTION_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverProductionIcons@PRODUCTION_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@SUPPORT_POWERS_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverSupportPowerIcons@SUPPORT_POWER_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@ARMY_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 400
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								ObserverArmyIcons@ARMY_ICONS:
									X: 160
									Y: 0
									Width: 0
									Height: PARENT_HEIGHT
									TooltipContainer: TOOLTIP_CONTAINER
						ScrollItem@COMBAT_PLAYER_TEMPLATE:
							X: 0
							Y: 0
							Width: 760
							Height: 24
							Background: scrollitem-nohover
							Children:
								ColorBlock@PLAYER_COLOR:
									X: 0
									Y: 0
									Width: PARENT_WIDTH - 200
									Height: PARENT_HEIGHT
								GradientColorBlock@PLAYER_GRADIENT:
									X: PARENT_WIDTH - 200
									Y: 0
									Width: 200
									Height: PARENT_HEIGHT
								Image@FLAG:
									X: 5
									Y: 4
									Width: 35
									Height: PARENT_HEIGHT - 4
									ImageName: random
									ImageCollection: flags
								Label@PLAYER:
									X: 40
									Y: 0
									Width: 120
									Height: PARENT_HEIGHT
									Font: Bold
									Shadow: True
								Label@ASSETS_DESTROYED:
									X: 160
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ASSETS_LOST:
									X: 235
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@UNITS_KILLED:
									X: 310
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@UNITS_DEAD:
									X: 385
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@BUILDINGS_KILLED:
									X: 460
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@BUILDINGS_DEAD:
									X: 535
									Y: 0
									Width: 75
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@ARMY_VALUE:
									X: 610
									Y: 0
									Width: 90
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
								Label@VISION:
									X: 700
									Y: 0
									Width: 60
									Height: PARENT_HEIGHT
									Align: Right
									Shadow: True
				Container@INCOME_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Color: 00000090
						LineGraph@INCOME_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_WIDTH - 5
							Height: PARENT_HEIGHT
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Earnings
							LabelFont: TinyBold
							AxisFont: TinyBold
				Container@ARMY_VALUE_GRAPH_CONTAINER:
					X: 0
					Y: 30
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Visible: False
					Children:
						ColorBlock@GRAPH_BACKGROUND:
							X: 0
							Y: 0
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Color: 00000090
						LineGraph@ARMY_VALUE_GRAPH:
							X: 0
							Y: 0
							Width: PARENT_WIDTH - 5
							Height: PARENT_HEIGHT
							ValueFormat: ${0}
							YAxisValueFormat: ${0:F0}
							XAxisSize: 40
							XAxisTicksPerLabel: 2
							XAxisLabel: Game Minute
							YAxisLabel: Army Value
							LabelFont: TinyBold
							AxisFont: TinyBold

Container@PLAYER_WIDGETS:
	Logic: LoadIngamePerfLogic, LoadIngameChatLogic
	Children:
		Container@CHAT_ROOT:
		Container@PERF_ROOT:
		Container@HPF_ROOT:
			Logic: LoadIngameHierarchicalPathFinderOverlayLogic
			X: WINDOW_WIDTH - WIDTH - 240
			Y: 40
			Width: 175
		ViewportController:
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
			TooltipTemplate: WORLD_TOOLTIP_FACTIONSUFFIX
			TooltipContainer: TOOLTIP_CONTAINER
			ZoomInKey: ZoomIn
			ZoomOutKey: ZoomOut
			ScrollUpKey: MapScrollUp
			ScrollDownKey: MapScrollDown
			ScrollLeftKey: MapScrollLeft
			ScrollRightKey: MapScrollRight
			JumpToTopEdgeKey: MapJumpToTopEdge
			JumpToBottomEdgeKey: MapJumpToBottomEdge
			JumpToLeftEdgeKey: MapJumpToLeftEdge
			JumpToRightEdgeKey: MapJumpToRightEdge
			BookmarkSaveKeyPrefix: MapBookmarkSave
			BookmarkRestoreKeyPrefix: MapBookmarkRestore
			BookmarkKeyCount: 4
		LogicKeyListener@PLAYER_KEYHANDLER:
			Logic: RemoveFromControlGroupHotkeyLogic
				RemoveFromControlGroupKey: RemoveFromControlGroup
		ControlGroups@CONTROLGROUPS:
			SelectGroupKeyPrefix: ControlGroupSelect
			CreateGroupKeyPrefix: ControlGroupCreate
			AddToGroupKeyPrefix: ControlGroupAddTo
			CombineWithGroupKeyPrefix: ControlGroupCombineWith
			JumpToGroupKeyPrefix: ControlGroupJumpTo
		LogicTicker@SIDEBAR_TICKER:
		Container@SUPPORT_POWERS:
			Logic: SupportPowerBinLogic
			X: 10
			Y: 10
			Children:
				Container@PALETTE_BACKGROUND:
					Children:
						Background@ICON_TEMPLATE:
							Logic: AddFactionSuffixLogic
							Width: 66
							Height: 50
							ClickThrough: false
							Background: panel-black
				SupportPowers@SUPPORT_PALETTE:
					IconSize: 64, 48
					X: 1
					Y: 1
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: SUPPORT_POWER_TOOLTIP_FACTIONSUFFIX
					ReadyText: supportpowers-support-powers-palette.ready
					HoldText: supportpowers-support-powers-palette.hold
					HotkeyPrefix: SupportPower
					HotkeyCount: 6
		Image@COMMAND_BAR_BACKGROUND:
			X: 5
			Y: WINDOW_HEIGHT - HEIGHT - 5
			Width: 494
			Height: 44
			ImageCollection: sidebar
			ImageName: background-commandbar
			ClickThrough: False
		Container@COMMAND_BAR:
			Logic: CommandBarLogic
				HighlightOnButtonPress: True
			X: 14
			Y: WINDOW_HEIGHT - HEIGHT - 14
			Width: 311
			Height: 26
			Children:
				LogicKeyListener@MODIFIER_OVERRIDES:
				WorldButton@ATTACK_MOVE:
					Logic: AddFactionSuffixLogic
					X: 0
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: AttackMove
					DisableKeySound: true
					TooltipText: button-command-bar-attack-move.tooltip
					TooltipDesc: button-command-bar-attack-move.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_WITH_DESC_HIGHLIGHT_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: attack-move
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@FORCE_MOVE:
					Logic: AddFactionSuffixLogic
					X: 39
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					DisableKeySound: true
					TooltipText: button-command-bar-force-move.tooltip
					TooltipDesc: button-command-bar-force-move.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_WITH_DESC_HIGHLIGHT_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: force-move
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@FORCE_ATTACK:
					Logic: AddFactionSuffixLogic
					X: 78
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					DisableKeySound: true
					TooltipText: button-command-bar-force-attack.tooltip
					TooltipDesc: button-command-bar-force-attack.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_WITH_DESC_HIGHLIGHT_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: force-attack
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@GUARD:
					Logic: AddFactionSuffixLogic
					X: 117
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: Guard
					DisableKeySound: true
					TooltipText: button-command-bar-guard.tooltip
					TooltipDesc: button-command-bar-guard.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: guard
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@DEPLOY:
					Logic: AddFactionSuffixLogic
					X: 156
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: Deploy
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-command-bar-deploy.tooltip
					TooltipDesc: button-command-bar-deploy.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: deploy
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@SCATTER:
					Logic: AddFactionSuffixLogic
					X: 195
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: Scatter
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-command-bar-scatter.tooltip
					TooltipDesc: button-command-bar-scatter.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: scatter
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@STOP:
					Logic: AddFactionSuffixLogic
					X: 234
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: Stop
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-command-bar-stop.tooltip
					TooltipDesc: button-command-bar-stop.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: stop
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@QUEUE_ORDERS:
					Logic: AddFactionSuffixLogic
					X: 273
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					DisableKeySound: true
					TooltipText: button-command-bar-queue-orders.tooltip
					TooltipDesc: button-command-bar-queue-orders.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_WITH_DESC_HIGHLIGHT_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 7
							Y: 1
							ImageCollection: command-icons
							ImageName: queue-orders
		Container@STANCE_BAR:
			Logic: StanceSelectorLogic
			X: 335
			Y: WINDOW_HEIGHT - HEIGHT - 14
			Width: 155
			Height: 26
			Children:
				WorldButton@STANCE_ATTACKANYTHING:
					Logic: AddFactionSuffixLogic
					X: 0
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: StanceAttackAnything
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-stance-bar-attackanything.tooltip
					TooltipDesc: button-stance-bar-attackanything.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 11
							Y: 5
							ImageCollection: stance-icons
							ImageName: attack-anything
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@STANCE_DEFEND:
					Logic: AddFactionSuffixLogic
					X: 39
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: StanceDefend
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-stance-bar-defend.tooltip
					TooltipDesc: button-stance-bar-defend.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 11
							Y: 5
							ImageCollection: stance-icons
							ImageName: defend
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@STANCE_RETURNFIRE:
					Logic: AddFactionSuffixLogic
					X: 78
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: StanceReturnFire
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-stance-bar-returnfire.tooltip
					TooltipDesc: button-stance-bar-returnfire.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 11
							Y: 5
							ImageCollection: stance-icons
							ImageName: return-fire
						Image@SEPARATOR:
							Logic: AddFactionSuffixLogic
							X: 38
							Y: 2
							ImageCollection: chrome-button
							ImageName: separator
				WorldButton@STANCE_HOLDFIRE:
					Logic: AddFactionSuffixLogic
					X: 117
					Y: 0
					Width: 38
					Height: 26
					Background: chrome-button-background
					Key: StanceHoldFire
					DisableKeyRepeat: true
					DisableKeySound: true
					TooltipText: button-stance-bar-holdfire.tooltip
					TooltipDesc: button-stance-bar-holdfire.tooltipdesc
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@ICON:
							X: 11
							Y: 5
							ImageCollection: stance-icons
							ImageName: hold-fire
		Container@MUTE_INDICATOR:
			Logic: MuteIndicatorLogic
			X: WINDOW_WIDTH - WIDTH - 240
			Y: 5
			Width: 200
			Height: 25
			Children:
				Image@ICON:
					X: PARENT_WIDTH - WIDTH
					Y: 1
					Width: 24
					Height: 24
					ImageCollection: sidebar-bits
					ImageName: indicator-muted
				Label@LABEL:
					Width: PARENT_WIDTH - 30
					Height: 25
					Align: Right
					Text: label-mute-indicator
					Contrast: true
		Image@SIDEBAR_BACKGROUND:
			X: WINDOW_WIDTH - WIDTH - 5
			Y: 5
			Width: 230
			Height: 314
			ImageCollection: sidebar
			ImageName: background-sidebar
			ClickThrough: false
			Children:
				Container@TOP_BUTTONS:
					Logic: MenuButtonsChromeLogic
					X: 18
					Y: 1
					Children:
						WorldButton@SELL_BUTTON:
							Logic: SellOrderButtonLogic, AddFactionSuffixLogic
							X: 0
							Width: 28
							Height: 26
							Background: chrome-button-background
							Key: Sell
							TooltipText: button-top-buttons-sell-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							VisualHeight: 0
							Children:
								Image@ICON:
									X: 6
									Y: 5
									ImageCollection: order-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 28
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						WorldButton@REPAIR_BUTTON:
							Logic: RepairOrderButtonLogic, AddFactionSuffixLogic
							X: 29
							Width: 28
							Height: 26
							Background: chrome-button-background
							Key: Repair
							TooltipText: button-top-buttons-repair-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							VisualHeight: 0
							Children:
								Image@ICON:
									X: 6
									Y: 5
									ImageCollection: order-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 28
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						WorldButton@BEACON_BUTTON:
							Logic: BeaconOrderButtonLogic, AddFactionSuffixLogic
							X: 58
							Width: 28
							Height: 26
							Background: chrome-button-background
							Key: PlaceBeacon
							TooltipText: button-top-buttons-beacon-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							VisualHeight: 0
							Children:
								Image@ICON:
									X: 6
									Y: 5
									ImageCollection: order-icons
						MenuButton@OPTIONS_BUTTON:
							Logic: AddFactionSuffixLogic
							Key: escape
							X: 156
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-top-buttons-options-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							DisableWorldSounds: true
							VisualHeight: 0
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: order-icons
									ImageName: options
				Container@RADAR:
					Logic: IngameRadarDisplayLogic
					X: 18
					Y: 39
					Children:
						LogicTicker@RADAR_TICKER:
						ColorBlock@RADAR_FADETOBLACK:
							Width: 194
							Height: 194
						Image@RADAR_LOGO:
							Logic: AddFactionSuffixLogic
							X: 31
							Y: 31
							ImageCollection: chrome-radar
							ImageName: logo
						Radar@RADAR_MINIMAP:
							WorldInteractionController: INTERACTION_CONTROLLER
							Width: 194
							Height: 194
							SoundUp: RadarUp
							SoundDown: RadarDown
						VideoPlayer@PLAYER:
							Width: 194
							Height: 194
							Skippable: false
				Container@POWERBAR_PANEL:
					Logic: IngamePowerBarLogic
					X: 0
					Y: 51
					Width: 10
					Height: 190
					Children:
						ResourceBar@POWERBAR:
							Width: 9
							Height: PARENT_HEIGHT - 2
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP_FACTIONSUFFIX
							IndicatorImage: indicator-left
						Image@POWERBAR_PANEL:
							ImageCollection: vertical-bars
							ImageName: power
				Container@SILOBAR_PANEL:
					Logic: IngameSiloBarLogic
					X: 219
					Y: 51
					Width: 10
					Height: 190
					Children:
						ResourceBar@SILOBAR:
							X: 1
							Width: 9
							Height: PARENT_HEIGHT - 2
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP_FACTIONSUFFIX
							IndicatorImage: indicator-right
						Image@SILOBAR_PANEL:
							ImageCollection: vertical-bars
							ImageName: silo
				Label@GAME_TIMER:
					Logic: GameTimerLogic
					X: 0
					Y: 234
					Width: PARENT_WIDTH
					Height: 22
					Align: Center
					Font: TinyBold
				WorldLabelWithTooltip@POWER:
					Logic: IngamePowerCounterLogic
					X: 35
					Y: 234
					Width: 50
					Height: 22
					Font: Bold
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: SIMPLE_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@POWER_ICON:
							X: 0 - 16
							Y: 3
							ImageCollection: power-icons
							ImageName: power-normal
				WorldLabelWithTooltip@CASH:
					Logic: IngameCashCounterLogic
					X: PARENT_WIDTH - WIDTH - 35
					Y: 234
					Width: 50
					Height: 22
					Align: Right
					Font: Bold
					TooltipContainer: TOOLTIP_CONTAINER
					TooltipTemplate: SIMPLE_TOOLTIP_FACTIONSUFFIX
					Children:
						Image@CASH_ICON:
							X: PARENT_WIDTH
							Y: 3
							ImageCollection: cash-icons
							ImageName: cash-normal
				Container@PRODUCTION_TYPES:
					X: 18
					Y: 267
					Width: 194
					Height: 26
					Children:
						ProductionTypeButton@BUILDING:
							Logic: AddFactionSuffixLogic
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-production-types-building-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							ProductionGroup: Building
							Key: ProductionTypeBuilding
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: production-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 38
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						ProductionTypeButton@SUPPORT:
							Logic: AddFactionSuffixLogic
							X: 39
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-production-types-support-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							ProductionGroup: Support
							Key: ProductionTypeDefense
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: production-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 38
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						ProductionTypeButton@INFANTRY:
							Logic: AddFactionSuffixLogic
							X: 78
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-production-types-infantry-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							ProductionGroup: Infantry
							Key: ProductionTypeInfantry
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: production-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 38
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						ProductionTypeButton@VEHICLE:
							Logic: AddFactionSuffixLogic
							X: 117
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-production-types-vehicle-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							ProductionGroup: Vehicle
							Key: ProductionTypeVehicle
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: production-icons
								Image@SEPARATOR:
									Logic: AddFactionSuffixLogic
									X: 38
									Y: 2
									ImageCollection: chrome-button
									ImageName: separator
						ProductionTypeButton@AIRCRAFT:
							Logic: AddFactionSuffixLogic
							X: 156
							Width: 38
							Height: 26
							Background: chrome-button-background
							TooltipText: button-production-types-aircraft-tooltip
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: BUTTON_TOOLTIP_FACTIONSUFFIX
							ProductionGroup: Aircraft
							Key: ProductionTypeAircraft
							Children:
								Image@ICON:
									X: 11
									Y: 5
									ImageCollection: production-icons
		Container@PRODUCTION_BACKGROUND:
			X: WINDOW_WIDTH - 5 - 18 - 194
			Y: 319
			Children:
				Background@ICON_TEMPLATE:
					Logic: AddFactionSuffixLogic
					Width: 66
					Height: 50
					Background: panel-black
		ProductionPalette@PRODUCTION_PALETTE:
			IconSize: 64, 48
			X: WINDOW_WIDTH - 5 - 17 - 194
			Y: 320
			Width: 192
			TooltipContainer: TOOLTIP_CONTAINER
			TooltipTemplate: PRODUCTION_TOOLTIP_FACTIONSUFFIX
			ReadyText: productionpalette-player-widgets-production-palette.ready
			HoldText: productionpalette-player-widgets-production-palette.hold
			HotkeyPrefix: Production
			HotkeyCount: 24
			SelectProductionBuildingHotkey: SelectProductionBuilding
		ProductionTabs@PRODUCTION_TABS:
			Logic: AddFactionSuffixLogic, ProductionTabsLogic
			PaletteWidget: PRODUCTION_PALETTE
			TypesContainer: PRODUCTION_TYPES
			BackgroundContainer: PRODUCTION_BACKGROUND
			PreviousProductionTabKey: PreviousProductionTab
			NextProductionTabKey: NextProductionTab
			X: WINDOW_WIDTH - 5 - 18 - 194
			Y: 299
			Width: 194
			Height: 20

Background@FMVPLAYER:
	Width: WINDOW_WIDTH
	Height: WINDOW_HEIGHT
	Background: panel-allblack
	Children:
		VideoPlayer@PLAYER:
			X: 0
			Y: 0
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
