## assetbrowser.yaml
label-assetbrowser-panel-title = Asset Browser
label-bg-source-selector-desc = Select asset source
dropdownbutton-bg-source-selector = Folders
dropdownbutton-bg-asset-types-dropdown = Asset types
label-bg-filename-desc = Filter by name
label-bg-sprite-scale = Scale:
label-bg-palette-desc = Palette:
label-sprite-bg-error = Error displaying file. See assetbrowser.log for details.

## color-picker.yaml
button-color-chooser-random = Random
button-color-chooser-store = Store
button-color-chooser-mixer-tab = Mixer
button-color-chooser-palette-tab = Palette
label-preset-header = Preset Colors
label-custom-header = Custom Colors

## connection.yaml
label-connecting-panel-title = Connecting
label-bg-connecting-desc = Connecting...
button-connecting-panel-abort = Abort
label-connection-background-connecting-desc = Failed to connect
label-connection-background-password = Password:
button-connectionfailed-panel-abort = Abort
label-connection-switchmod-panel-title = Switch Mod
label-connection-background-desc = This server is running a different mod:
label-connection-background-desc2 = Switch mods and join server?
button-connection-switchmod-panel-abort = Abort
button-connection-switchmod-panel-switch = Switch

## credits.yaml
label-openra = OpenRA

## credits.yaml, mainmenu.yaml
label-credits-title = Credits

## dialogs.yaml
button-prompt-confirm = Confirm
button-prompt-other = Restart
button-text-input-prompt-accept = OK

## editor.yaml
label-new-map-bg-title = New Map
label-bg-tileset = Tileset:
label-bg-width = Width:
label-bg-height = Height:
button-new-map-bg-create = Create
label-save-map-panel-title = Save Map
label-save-map-background-title = Title:
label-save-map-background-author = Author:
label-save-map-background-visibility = Visibility:
dropdownbutton-save-map-background-visibility-dropdown = Map Visibility
label-save-map-background-directory = Directory:
label-save-map-background-filename = Filename:
button-save-map-panel = Save
label-actor-edit-panel-id = ID
button-container-ok = OK
button-editor-world-root-options-tooltip = Menu
label-tiles-bg-search = Search:
label-bg-filter = Filter:
label-actors-bg-search = Search:
label-actor-bg-owner = Owner:
label-area-selection = Area Selection
label-area-info = Area Info
label-selected-area-diagonal = Diagonal:
label-selected-area-resources = Resources:
label-selection-filters = Filters
label-filter-terrain = Terrain
label-filter-resources = Resources
label-filter-actors = Actors
label-tool-bg-category = Tool:
button-marker-tiles-clear-current = Clear Current
button-marker-tiles-clear-all = Clear All
label-marker-layer-num-sides = Number of Sides
label-marker-alpha = Tile Alpha
label-marker-mirror-mode = Mirror Mode
label-marker-axis-angle = Axis Angle

button-map-editor-tab-container-select-tooltip = Selection
button-map-editor-tab-container-tiles-tooltip = Tiles
button-map-editor-tab-container-overlays-tooltip = Overlays
button-map-editor-tab-container-actors-tooltip = Actors
button-map-editor-tab-container-tools-tooltip = Tools
button-map-editor-tab-container-history-tooltip = History

button-delete-area =
    .label = Delete
    .tooltip = Remove everything in the selected area

button-delete-actor =
    .label = Delete
    .tooltip = Remove the selected actor

button-editor-world-root-copy =
    .label = Copy
    .tooltip = Copy the selected area

button-editor-world-root-paste =
    .label = Paste
    .tooltip = Paste the saved area

button-editor-world-root-undo =
    .label = Undo
    .tooltip = Undo last step

button-editor-world-root-redo =
    .label = Redo
    .tooltip = Redo last step

dropdownbutton-editor-world-root-overlay-button = Overlays
button-select-categories-buttons-all = All
button-select-categories-buttons-none = None

label-tool-marker-tiles = Marker Tiles

## encyclopedia.yaml, mainmenu.yaml
label-encyclopedia-title = EVA Database

## gamesave-browser.yaml
label-gamesave-browser-panel-load-title = Load game
label-gamesave-browser-panel-save-title = Save game
label-bg-title = [CREATE NEW FILE]
button-bg-delete-all = Delete All
button-bg-delete = Delete
button-bg-rename = Rename
button-bg-load = Load
button-bg-save = Save

## gamesave-loading.yaml
label-gamesave-loading-screen-title = Loading Saved Game
label-gamesave-loading-screen-desc = Press Escape to cancel loading and return to the main menu

## ingame-chat.yaml, ingame-infochat.yaml
button-chat-chrome-mode =
    .label = Team
    .tooltip = Toggle chat mode

## ingame-debug.yaml
label-debug-panel-title = Debug Options
checkbox-debug-panel-instant-build = Instant Build Speed
checkbox-debug-panel-enable-tech = Build Everything
checkbox-debug-panel-build-anywhere = Build Anywhere
checkbox-debug-panel-unlimited-power = Unlimited Power
checkbox-debug-panel-instant-charge = Instant Charge Time
checkbox-debug-panel-disable-visibility-checks = Disable Visibility Checks
button-debug-panel-give-cash = Give $20,000
button-debug-panel-grow-resources = Grow Resources
button-debug-panel-give-exploration = Clear Shroud
button-debug-panel-reset-exploration = Reset Shroud
label-debug-panel-visualizations-title = Visualizations
checkbox-debug-panel-show-unit-paths = Show Unit Paths
checkbox-debug-panel-show-customterrain-overlay = Show Custom Terrain
checkbox-debug-panel-show-actor-tags = Show Actor Tags
checkbox-debug-panel-show-combatoverlay = Show Combat Geometry
checkbox-debug-panel-show-geometry = Show Render Geometry
checkbox-debug-panel-show-terrain-overlay = Show Terrain Geometry
checkbox-debug-panel-show-screenmap = Show Screen Map

## ingame-debug-hpf.yaml
dropdownbutton-hpf-overlay-locomotor = Select Locomotor
dropdownbutton-hpf-overlay-check = Select BlockedByActor

## ingame-info.yaml
label-game-info-panel-title = Game Information

## ingame-infoobjectives.yaml
label-mission-objectives = Mission:

## ingame-infoscripterror.yaml
label-script-error-panel-desc-a = The map script has encountered a fatal error
label-script-error-panel-desc-b = The details of the error have been saved to lua.log in the logs directory.
label-script-error-panel-desc-c = Please send this file to the map author so that they can fix this issue.

## ingame-infostats.yaml
label-mission-objective = Mission:
checkbox-stats-objective = Destroy all opposition!
label-stats-player = Player
label-stats-faction = Faction
label-stats-score = Score
label-stats-actions = Actions

## ingame.yaml
button-observer-widget-options-tooltip = Menu
button-replay-player-pause-tooltip = Pause
button-replay-player-play-tooltip = Play

button-replay-player-slow =
    .tooltip = Slow speed
    .label = 50%

button-replay-player-regular =
    .tooltip = Regular speed
    .label = 100%

button-replay-player-fast =
    .tooltip = Fast speed
    .label = 200%

button-replay-player-maximum =
    .tooltip = Maximum speed
    .label = MAX

label-basic-stats-player-header = Player
label-basic-stats-cash-header = Cash
label-basic-stats-power-header = Power
label-basic-stats-kills-header = Kills
label-basic-stats-deaths-header = Deaths
label-basic-stats-assets-destroyed-header = Destroyed
label-basic-stats-assets-lost-header = Lost
label-basic-stats-experience-header = Score
label-basic-stats-actions-min-header = APM
label-economy-stats-player-header = Player
label-economy-stats-cash-header = Cash
label-economy-stats-income-header = Income
label-economy-stats-assets-header = Assets
label-economy-stats-earned-header = Earned
label-economy-stats-spent-header = Spent
label-economy-stats-harvesters-header = Harvesters
label-economy-stats-derricks-header = Oil Derricks
label-production-stats-player-header = Player
label-production-stats-header = Production
label-support-powers-player-header = Player
label-support-powers-header = Support Powers
label-army-player-header = Player
label-army-header = Army
label-combat-stats-player-header = Player
label-combat-stats-assets-destroyed-header = Destroyed
label-combat-stats-assets-lost-header = Lost
label-combat-stats-units-killed-header = U. Killed
label-combat-stats-units-dead-header = Units Lost
label-combat-stats-buildings-killed-header = B. Killed
label-combat-stats-buildings-dead-header = B. Lost
label-combat-stats-army-value-header = Army Value
label-combat-stats-vision-header = Vision

supportpowers-support-powers-palette =
    .ready = Ready
    .hold = On Hold

button-command-bar-attack-move =
    .tooltip = Attack Move
    .tooltipdesc =
    Selected units will move to the desired location
    and attack any enemies they encounter en route.

    Hold <(Ctrl)> while targeting to order an Assault Move
    that attacks any units or structures encountered en route.

    Left-click icon then right-click on target location.

button-command-bar-force-move =
    .tooltip = Force Move
    .tooltipdesc =
    Selected units will move to the desired location
     - Default activity for the target is suppressed
     - Vehicles will attempt to crush enemies at the target location
     - Helicopters will land at the target location

    Left-click icon then right-click on target.
    Hold <(Alt)> to activate temporarily while commanding units.

button-command-bar-force-attack =
    .tooltip = Force Attack
    .tooltipdesc =
    Selected units will attack the targeted unit or location
     - Default activity for the target is suppressed
     - Allows targeting of own or ally forces
     - Long-range artillery units will always target the
       location, ignoring units and buildings

    Left-click icon then right-click on target.
    Hold <(Ctrl)> to activate temporarily while commanding units.

button-command-bar-guard =
    .tooltip = Guard
    .tooltipdesc =
    Selected units will follow the targeted unit.

    Left-click icon then right-click on target unit.

button-command-bar-deploy =
    .tooltip = Deploy
    .tooltipdesc =
    Selected units will perform their default deploy activity
     - MCVs will unpack into a Construction Yard
     - Construction Yards will re-pack into a MCV
     - Transports will unload their passengers

    Acts immediately on selected units.

button-command-bar-scatter =
    .tooltip = Scatter
    .tooltipdesc =
    Selected units will stop their current activity
    and move to a nearby location.

    Acts immediately on selected units.

button-command-bar-stop =
    .tooltip = Stop
    .tooltipdesc =
    Selected units will stop their current activity.
    Selected buildings will reset their rally point.

    Acts immediately on selected targets.

button-command-bar-queue-orders =
    .tooltip = Waypoint Mode
    .tooltipdesc =
    Use Waypoint Mode to give multiple linking commands
    to the selected units. Units will execute the commands
    immediately upon receiving them.

    Left-click icon then give commands in the game world.
    Hold <(Shift)> to activate temporarily while commanding units.

button-stance-bar-attackanything =
    .tooltip = Attack Anything Stance
    .tooltipdesc =
    Set the selected units to Attack Anything stance:
     - Units will attack enemy units and structures on sight
     - Units will pursue attackers across the battlefield

button-stance-bar-defend =
    .tooltip = Defend Stance
    .tooltipdesc =
    Set the selected units to Defend stance:
     - Units will attack enemy units on sight
     - Units will not move or pursue enemies

button-stance-bar-returnfire =
    .tooltip = Return Fire Stance
    .tooltipdesc =
    Set the selected units to Return Fire stance:
     - Units will retaliate against enemies that attack them
     - Units will not move or pursue enemies

button-stance-bar-holdfire =
    .tooltip = Hold Fire Stance
    .tooltipdesc =
    Set the selected units to Hold Fire stance:
     - Units will not fire upon enemies
     - Units will not move or pursue enemies

label-mute-indicator = Audio Muted
button-top-buttons-sell-tooltip = Sell
button-top-buttons-repair-tooltip = Repair
button-top-buttons-beacon-tooltip = Place Beacon
button-top-buttons-options-tooltip = Options
button-production-types-building-tooltip = Buildings
button-production-types-support-tooltip = Support
button-production-types-infantry-tooltip = Infantry
button-production-types-vehicle-tooltip = Vehicles
button-production-types-aircraft-tooltip = Aircraft

productionpalette-player-widgets-production-palette =
    .ready = Ready
    .hold = On Hold

## lobby-kickdialogs.yaml
label-kick-client-dialog-text-a = You may also apply a temporary ban, preventing
label-kick-client-dialog-text-b = them from joining for the remainder of this game.
checkbox-kick-client-dialog-prevent-rejoining = Temporarily Ban
button-kick-client-dialog = Kick
label-kick-spectators-dialog-title = Kick Spectators
button-kick-spectators-dialog-ok = Ok
label-force-start-dialog-title = Start Game?
label-force-start-dialog-text-a = One or more players are not yet ready.
label-force-start-dialog-text-b = Are you sure that you want to force start the game?
label-kick-warning-a = One or more clients are missing the selected
label-kick-warning-b = map, and will be kicked from the server.
button-force-start-dialog-start = Start

## lobby-mappreview.yaml
label-map-incompatible-status-a = This map is not compatible
label-map-incompatible-status-b = with this version of OpenRA
label-map-validating-status = Validating...
button-map-download-available-install = Install Map
button-map-preview-update = Update Map
button-map-update-download-available-install = Install Map
label-map-preview-searching = Searching OpenRA Resource Center...
label-map-unavailable-a = This map was not found on the
label-map-unavailable-b = OpenRA Resource Center
label-map-preview-error = An error occurred during installation
label-map-update-available-a = A new version of the map
label-map-update-available-b = was found on your computer

## lobby-music.yaml
label-music-controls-volume = Volume:

## lobby-music.yaml, lobby.yaml, mainmenu.yaml
label-music-title = Music

## music.yaml
label-music-title-panel-title = Music Player

## lobby-music.yaml, music.yaml
label-music-controls-length = Length
checkbox-music-controls-shuffle = Shuffle
checkbox-music-controls-loop = Loop
label-container-title = Track
label-no-music-title = Music Not Installed
label-no-music-desc-a = The game music can be installed
label-no-music-desc-b = from the "Manage Content" menu.

## lobby-options.yaml
label-lobby-options-bin-title = Map Options

## lobby-players.yaml
label-lobby-players-player = Player
label-lobby-players-color = Color
label-lobby-players-faction = Faction
label-lobby-players-team = Team
label-lobby-players-handicap = Handicap
label-lobby-players-spawn = Spawn
label-lobby-players-ready = Ready
label-lobby-players-name = Name
dropdownbutton-lobby-players-handicap-tooltip = A handicap decreases the combat effectiveness of the player's forces
button-lobby-players-join = Play in this slot
label-lobby-players-spectator = Spectator
checkbox-lobby-players-new-spectator-toggle = Allow Spectators?
button-lobby-players-spectate = Spectate

## lobby-servers.yaml
image-lobby-servers-bin-password-protected-tooltip = Requires Password
image-lobby-servers-bin-requires-authentication-tooltip = Requires OpenRA forum account
dropdownbutton-lobby-servers-bin-filters = Filter Games

## lobby.yaml
dropdownbutton-bg-slots = Slot Admin
button-bg-reset-options = Reset Defaults
button-skirmish-tabs-players-tab = Players
button-skirmish-tabs-options-tab = Options
button-multiplayer-tabs-players-tab = Players
button-multiplayer-tabs-options-tab = Options
button-multiplayer-tabs-servers-tab = Servers
button-bg-changemap = Change Map

button-lobbychat-chat-mode =
    .label = Team
    .tooltip = Toggle chat mode

button-server-lobby-disconnect = Leave Game
button-server-lobby-start-game = Start Game

## mainmenu-prompts.yaml
label-mainmenu-introduction-prompt-title = Establishing Battlefield Control
label-bg-desc-a = Welcome back Commander! Initialize combat parameters using the options below.
label-bg-desc-b = Additional options can be configured later from the Settings menu.
label-mainmenu-system-info-prompt-title = Establishing Battlefield Control
label-bg-prompt-text-a = We would like to collect some details that will help us optimize OpenRA.
label-bg-prompt-text-b = With your permission, the following anonymous system data will be sent:
checkbox-bg-sysinfo = Send System Information

## mainmenu-prompts.yaml, settings-display.yaml
label-profile-section-header = Profile
label-player-container = Player Name:
label-playercolor-container-color = Preferred Color:
label-display-section-header = Display
label-battlefield-camera-dropdown = Battlefield Camera:
label-ui-scale-dropdown = UI Scale:
checkbox-cursordouble-container = Increase Cursor Size

## mainmenu-prompts.yaml, settings-input.yaml
label-input-section-header = Input
label-mouse-control-container = Control Scheme:
label-mouse-control-desc-classic-selection = - Select units using the <Left> mouse button
label-mouse-control-desc-classic-commands = - Command units using the <Left> mouse button
label-mouse-control-desc-classic-buildings = - Place structures using the <Left> mouse button
label-mouse-control-desc-classic-support = - Target support powers using the <Left> mouse button
label-mouse-control-desc-classic-zoom = - Zoom the battlefield using the <Scroll Wheel>
label-mouse-control-desc-classic-zoom-modifier = - Zoom the battlefield using <MODIFIER + Scroll Wheel>
label-mouse-control-desc-classic-scroll-right = - Pan the battlefield using the <Right> mouse button
label-mouse-control-desc-classic-scroll-middle = - Pan the battlefield using the <Middle> mouse button
label-mouse-control-desc-classic-edgescroll = or by moving the cursor to the edge of the screen
label-mouse-control-desc-modern-selection = - Select units using the <Left> mouse button
label-mouse-control-desc-modern-commands = - Command units using the <Right> mouse button
label-mouse-control-desc-modern-buildings = - Place structures using the <Left> mouse button
label-mouse-control-desc-modern-support = - Target support powers using the <Left> mouse button
label-mouse-control-desc-modern-zoom = - Zoom the battlefield using the <Scroll Wheel>
label-mouse-control-desc-modern-zoom-modifier = - Zoom the battlefield using <MODIFIER + Scroll Wheel>
label-mouse-control-desc-modern-scroll-right = - Pan the battlefield using the <Right> mouse button
label-mouse-control-desc-modern-scroll-middle = - Pan the battlefield using the <Middle> mouse button
label-mouse-control-desc-modern-edgescroll = or by moving the cursor to the edge of the screen
checkbox-edgescroll-container = Screen Edge Panning

## mainmenu.yaml
label-singleplayer-title = Singleplayer
label-main-menu-mainmenu-title = Main Menu
button-extras-title = Extras
button-main-menu-content = Manage Content
button-singleplayer-menu-skirmish = Skirmish
button-singleplayer-menu-load = Load
button-extras-menu-replays = Replays
label-map-editor-title = Map Editor
button-extras-menu-assetbrowser = Asset Browser
button-map-editor-new-map = New Map
button-map-editor-load-map = Load Map
dropdownbutton-news-bg-button = Battlefield News
label-update-notice-a = You are running an outdated version of OpenRA.
label-update-notice-b = Download the latest version from www.openra.net

## mainmenu.yaml, missionbrowser.yaml
label-missions-title = Missions

## mainmenu.yaml, multiplayer-browser.yaml
label-multiplayer-title = Multiplayer

## mainmenu.yaml, settings.yaml
button-settings-title = Settings

## mapchooser.yaml
label-mapchooser-panel-title = Select Map
button-bg-system-maps-tab = Official Maps
button-bg-remote-maps-tab = Server Maps
button-bg-user-maps-tab = Custom Maps
label-filter-order-controls-desc = Filter:
label-filter-order-controls-desc-joiner = in
label-filter-order-controls-orderby = Order by:
button-bg-randommap = Random
button-bg-delete-map = Delete Map
button-bg-delete-all-maps = Delete All Maps
button-bg-ok = Ok

## missionbrowser.yaml
button-missionbrowser-panel-mission-info = Mission Info
button-missionbrowser-panel-mission-options = Options
button-missionbrowser-panel-start-briefing-video = Watch Briefing
button-missionbrowser-panel-stop-briefing-video = Stop Briefing
button-missionbrowser-panel-start-info-video = Watch Info Video
button-missionbrowser-panel-stop-info-video = Stop Info Video
button-missionbrowser-panel-play = Play
dropdown-missionbrowser-difficulty =
    .label = Difficulty
    .description = The difficulty of the mission

dropdown-missionbrowser-gamespeed = Speed:
label-missionbrowser-normal-difficulty = Normal

## multiplayer-browser.yaml
image-bg-password-protected-tooltip = Requires Password
image-bg-requires-authentication-tooltip = Requires OpenRA forum account
button-selected-server-join = Join
dropdownbutton-bg-filters = Filter Games
button-bg-directconnect = Direct IP
button-bg-create = Create

## multiplayer-browser.yaml, lobby-servers.yaml
label-container-server = Server
label-container-players = Players
label-container-location = Location
label-container-status = Status
label-bg-outdated-version = You are running an outdated version of OpenRA. Download the latest version from www.openra.net
label-bg-unknown-version = You are running an unrecognized version of OpenRA. Download the latest version from www.openra.net
label-bg-playtest-available = A preview of the next OpenRA release is available for testing. Download the playtest from www.openra.net

## multiplayer-browserpanels.yaml
checkbox-multiplayer-filter-panel-waiting-for-players = Waiting
checkbox-multiplayer-filter-panel-empty = Empty
checkbox-multiplayer-filter-panel-password-protected = Protected
checkbox-multiplayer-filter-panel-already-started = Started
checkbox-multiplayer-filter-panel-incompatible-version = Incompatible

## multiplayer-createserver.yaml
label-multiplayer-createserver-panel-title = Create Server
label-bg-server-name = Server Name:
label-bg-password = Password:
label-bg-after-password = (optional)
label-bg-listen-port = Port:
checkbox-bg-advertise = Advertise Online
label-notices-lan-advertising = - Game will be advertised to the Local Area Network only.
label-notices-lan-firewall = - You must manually configure your firewall to allow connections.
label-notices-lan-portforward-a = - Players can connect using Direct IP from the Internet if you
label-notices-lan-portforward-b = manually configure port forwarding on your router.
label-notices-no-upnp-advertising = - Game will be advertised to the Local Area Network and Internet.
label-notices-no-upnp-firewall = - You must manually configure your firewall to allow connections.
label-notices-no-upnp-portforward-a = - You must manually configure your router to allow and forward
label-notices-no-upnp-portforward-b = connections to your local IP and Port.
label-notices-no-upnp-settings-a = - You can enable UPnP/NAT-PMP (if supported by your router)
label-notices-no-upnp-settings-b = in the Advanced tab of the settings menu.
label-notices-upnp-advertising = - Game will be advertised to the Local Area Network and Internet.
label-notices-upnp-firewall = - You must manually configure your firewall to allow connections.
label-notices-upnp-portforward-a = - Game will automatically configure port forwarding.
label-notices-upnp-settings-a = - You can disable UPnP/NAT-PMP in the settings menu.
button-multiplayer-createserver-panel-change-map = Change Map
button-multiplayer-createserver-panel-create = Create

## multiplayer-directconnect.yaml
label-directconnect-panel-title = Connect to Server
label-bg-address = Address:
label-bg-port = Port:
button-directconnect-panel-join = Join

## playerprofile.yaml
button-profile-header-logout = Logout
label-generate-keys-desc-a = Connect to a forum account to identify
label-generate-keys-desc-b = yourself to other players, join private
label-generate-keys-desc-c = servers, and display badges.
button-generate-keys-key = Connect to an OpenRA forum account
label-generating-keys-desc-a = Generating authentication key pair.
label-generating-keys-desc-b = This will take several seconds...
label-register-fingerprint-desc-a = An authentication key has been copied to your
label-register-fingerprint-desc-b = clipboard. Add this to your User Control Panel
label-register-fingerprint-desc-c = on the OpenRA forum then press Continue.
label-checking-fingerprint-desc-a = Querying account details from
label-checking-fingerprint-desc-b = the OpenRA forum...
label-fingerprint-not-found-desc-a = Your authentication key is not connected
label-fingerprint-not-found-desc-b = to an OpenRA forum account.
label-connection-error-desc-a = Failed to connect to the OpenRA forum.
label-connection-error-desc-b = Please check your internet connection.

## replaybrowser.yaml
label-replaybrowser-panel-title = Replay Viewer
label-filters-title = Filter
label-filters-flt-gametype-desc = Type:
dropdownbutton-filters-any = Any
label-filters-flt-date-desc = Date:
label-filters-flt-duration-desc = Duration:
label-filters-flt-mapname-desc = Map:
label-filters-flt-player-desc = Player:
dropdownbutton-filters-flt-player = Anyone
label-filters-flt-outcome-desc = Outcome:
label-filters-flt-faction-desc = Faction:
button-filters-flt-reset = Reset Filters
label-management-manage-title = Manage
button-management-mng-rensel = Rename
button-management-mng-delsel = Delete
button-management-mng-delall = Delete All
label-replay-list-container-replaybrowser-title = Choose Replay
button-replaybrowser-panel-watch = Watch

## settings-advanced.yaml
label-network-section-header = Advanced
checkbox-nat-discovery-container = Enable UPnP/NAT-PMP Discovery
checkbox-fetch-news-container = Fetch Community News
checkbox-perfgraph-container = Show Performance Graph
checkbox-check-version-container = Check for Updates
checkbox-perftext-container = Show Performance Text
checkbox-sendsysinfo-container = Send System Information
label-sendsysinfo-checkbox-container-desc = Your Operating System, OpenGL and .NET runtime versions, and language settings will be sent along with an anonymous ID to help prioritize future development.
label-debug-section-header = Developer
label-debug-hidden-container-a = Additional developer-specific options can be enabled via the
label-debug-hidden-container-b = Debug.DisplayDeveloperSettings setting or launch flag
checkbox-botdebug-container = Show Bot Debug Messages
checkbox-checkbotsync-container = Check Sync around BotModule Code
checkbox-luadebug-container = Show Map Debug Messages
checkbox-checkunsynced-container = Check Sync around Unsynced Code
checkbox-replay-commands-container = Enable Debug Commands in Replays
checkbox-perflogging-container = Enable Tick Performance Logging

## settings-audio.yaml
label-audio-section-header = Audio
label-no-audio-device-container = Audio controls require an active sound device
checkbox-cash-ticks-container = Cash Ticks
checkbox-mute-sound-container = Mute Sound
label-sound-volume-container = Sound Volume:

checkbox-mute-background-music-container =
    .label = Mute Menu Music
    .tooltip = Mute background music when no specific track is playing

label-music-title-volume-container = Music Volume:
label-audio-device-container = Audio Device:
label-video-volume-container = Video Volume:
label-restart-required-container-audio-desc = Device changes will be applied after the game is restarted

## settings-display.yaml
label-target-lines-dropdown-container = Target Lines:
label-status-bar-dropdown-container-bars = Status Bars:

checkbox-player-stance-colors-container =
    .label = Player Relationship Colors
    .tooltip = Change player colors based on relationship (own, enemy, ally, neutral)

checkbox-ui-feedback-container =
    .label = Show UI Feedback Notifications
    .tooltip = Show transient text notifications for UI events

checkbox-transients-container =
    .label = Show Game Event Notifications
    .tooltip = Show transient text notifications for game events

checkbox-hide-replay-chat-container = Hide Chat in Replays
label-video-section-header = Video
label-video-mode-dropdown-container = Video Mode:
dropdownbutton-video-mode-dropdown-container = Windowed
label-window-resolution-container-size = Window Size:
label-window-resolution-container-x = x
label-display-selection-container = Select Display:
dropdownbutton-display-selection-container-dropdown = Standard
checkbox-vsync-container = Enable VSync
checkbox-frame-limit-gamespeed-container = Limit framerate to game tick rate
label-gl-profile-dropdown-container = OpenGL Profile:
label-restart-required-container-video-desc = Display and OpenGL changes require restart

## settings-hotkeys.yaml
hotkey-group-game-commands = Game Commands
hotkey-group-viewport-commands = Viewport Commands
hotkey-group-observer-replay-commands = Observer / Replay Commands
hotkey-group-unit-commands = Unit Commands
hotkey-group-unit-stance-commands = Unit Stance Commands
hotkey-group-production-commands = Production Commands
hotkey-group-support-power-commands = Support Power Commands
hotkey-group-music-commands = Music Commands
hotkey-group-chat-commands = Chat Commands
hotkey-group-control-groups = Control Groups
hotkey-group-editor-commands = Editor Commands
label-hotkeys-panel-filter-input = Filter by name:
label-hotkeys-panel-context-dropdown = Context:
label-hotkey-empty-list-message = No hotkeys match the filter criteria.
label-notices-readonly-notice = This hotkey cannot be modified
button-hotkey-remap-dialog-override = Override

button-hotkey-remap-dialog-clear =
    .label = Clear
    .tooltip = Unbind the hotkey

button-hotkey-remap-dialog-reset =
    .label = Reset
    .tooltip = Reset to default

## settings-input.yaml
label-zoom-modifier-container = Zoom Modifier:
checkbox-alternate-scroll-container = Alternate Mouse Panning
checkbox-lockmouse-container = Lock Mouse to Window
label-mouse-scroll-type-container = Pan Behaviour:
label-scrollspeed-slider-container-scroll-speed = Pan Speed:
label-zoomspeed-slider-container-zoom-speed = Zoom Speed:
label-ui-scrollspeed-slider-container-scroll-speed = UI Scroll Speed:

## settings.yaml
button-settings-panel-reset = Reset

## tooltips.yaml
label-latency-tooltip-prefix = Latency:
label-anonymous-player-tooltip-name = Anonymous Player
label-bot-player-tooltip-name = Bot
label-bot-managed-by-tooltip = Bot managed by { $name }
label-game-admin = Game Admin

