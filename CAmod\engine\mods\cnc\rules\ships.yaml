BOAT:
	Inherits: ^Ship
	Inherits@SELECTABLE: ^SelectableCombatUnit
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAll
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-boat-name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Health:
		HP: 70000
	Armor:
		Type: Heavy
	TDGunboat:
		Speed: 29
	RevealsShroud:
		Range: 7c0
		Type: CenterPosition
	Turreted:
		TurnSpeed: 20
		Offset: 0,896,171
	Armament:
		Weapon: BoatMissile
		LocalOffset: 85,-85,0, 85,85,0
	AttackMove:
		Voice: Attack
	AttackTDGunboatTurreted:
		Voice: Attack
	-QuantizeFacingsFromSequence:
	BodyOrientation:
		QuantizedFacings: 2
	WithGunboatBody:
		Sequence: left # Just a work-around to avoid crash
	Selectable:
		Bounds: 1792, 1024
		DecorationBounds: 1792, 1024
	AutoTarget:
		AllowMovement: false
	RejectsOrders:
	GrantConditionOnDamageState@HEAVY:
		Condition: heavy-damage
		ValidDamageStates: Heavy
	GrantConditionOnDamageState@CRITICAL:
		Condition: critical-damage
		ValidDamageStates: Critical
	SpeedMultiplier@HEAVYDAMAGE:
		RequiresCondition: heavy-damage
		Modifier: 75
	SpeedMultiplier@CRITICALDAMAGE:
		RequiresCondition: critical-damage
		Modifier: 50

LST:
	Inherits: ^Ship
	Inherits@TRANSPORT: ^Transport
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-lst-name
	Mobile:
		Locomotor: lcraft
		InitialFacing: 0
		TurnSpeed: 16
		Speed: 170
		PauseOnCondition: notmobile
	Health:
		HP: 40000
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 7c0
	-BodyOrientation:
	ClassicFacingBodyOrientation:
	WithFacingSpriteBody:
	Interactable:
		Bounds: 2048, 2048
	WithCargo:
		DisplayTypes: Infantry, Vehicle
		LocalOffset: 390,-256,0, 390,256,0, 0,0,0, -390,-256,0, -390,256,0
	Cargo:
		Types: disabled
		MaxWeight: 5
		UnloadVoice: Unload
		PassengerFacing: 0
		LoadingCondition: notmobile
	RejectsOrders:
