C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\lua51.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.exe
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.dll.config
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.deps.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.runtimeconfig.json
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\bin\OpenRA.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.AssemblyInfo.cs
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.csproj.CopyComplete
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\refint\OpenRA.dll
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.pdb
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\OpenRA.Launcher.genruntimeconfig.cache
C:\Users\<USER>\Documents\GitHub\cnc-ca-ai\CAmod\engine\OpenRA.Launcher\obj\Release\ref\OpenRA.dll
