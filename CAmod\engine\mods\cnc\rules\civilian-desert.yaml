V20:
	Inherits: ^CivBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -840,-512,0, 0,0,0, -840,512,0
		Type: Rectangle
			TopLeft: -1024, -512
			BottomRight: 1024, 896
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	SpawnActorOnDeath:
		Actor: V20.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v20-name

V20.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v20-husk-name

V21:
	Inherits: ^CivBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 840,-512,0, 420,0,0, 840,512,0
		Type: Rectangle
			TopLeft: -1024, -1024
			BottomRight: 1024, 0
	HitShape@WELL:
		TargetableOffsets: -770,512,0
		Type: Rectangle
			TopLeft: 0, 0
			BottomRight: 1024, 598
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	SpawnActorOnDeath:
		Actor: V21.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v21-name

V21.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v21-husk-name

V22:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V22.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v22-name

V22.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v22-husk-name

V23:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V23.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v23-name

V23.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v23-husk-name

V24:
	Inherits: ^CivBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: -630,-512,0, 0,0,0, -630,256,0, 420,-512,0
		Type: Rectangle
			TopLeft: -1024, -683
			BottomRight: 640, 853
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	SpawnActorOnDeath:
		Actor: V24.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v24-name

V24.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v24-husk-name

V25:
	Inherits: ^CivBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,-128,0, 420,512,0
		Type: Rectangle
			TopLeft: -683, -683
			BottomRight: 1024, 512
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: actor-v25-name
	SpawnActorOnDeath:
		Actor: V25.Husk
	MapEditorData:
		RequireTilesets: DESERT

V25.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx xx
		Dimensions: 2,2
	Tooltip:
		Name: actor-v25-husk-name
	MapEditorData:
		RequireTilesets: DESERT

V26:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V26.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v26-name

V26.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v26-husk-name

V27:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V27.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v27-name

V27.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v27-husk-name

V28:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V28.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v28-name

V28.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v28-husk-name

V29:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V29.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v29-name

V29.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v29-husk-name

V30:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V30.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v30-name

V30.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v30-husk-name

V31:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V31.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v31-name

V31.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v31-husk-name

V32:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V32.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v32-name

V32.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v32-husk-name

V33:
	Inherits: ^CivBuilding
	Inherits@shape: ^2x1Shape
	Building:
		Footprint: xx
		Dimensions: 2,1
	SpawnActorOnDeath:
		Actor: V33.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v33-name

V33.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: xx
		Dimensions: 2,1
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v33-husk-name

V34:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V34.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v34-name

V34.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v34-husk-name

V35:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V35.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v35-name

V35.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v35-husk-name

V36:
	Inherits: ^CivBuilding
	SpawnActorOnDeath:
		Actor: V36.Husk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v36-name

V36.Husk:
	Inherits: ^CivBuildingHusk
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v36-husk-name

V37:
	Inherits: ^CivBuilding
	HitShape:
		UseTargetableCellsOffsets: false
		TargetableOffsets: 0,0,0, 0,1024,0
		Type: Rectangle
			TopLeft: -512, -597
			BottomRight: 1536, 597
	SpawnActorOnDeath:
		Actor: V37.Husk
	Building:
		Footprint: __xx_ ___xx
		Dimensions: 5,2
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v37-name

V37.Husk:
	Inherits: ^CivBuildingHusk
	Building:
		Footprint: __xx_ ___xx
		Dimensions: 5,2
	MapEditorData:
		RequireTilesets: DESERT
	Tooltip:
		Name: actor-v37-husk-name
