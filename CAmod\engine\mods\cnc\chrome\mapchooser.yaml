Container@MAPCHOOSER_PANEL:
	Logic: MapChooserLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 900
	Height: 540
	Children:
		Label@TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: label-mapchooser-panel-title
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				But<PERSON>@SYSTEM_MAPS_TAB_BUTTON:
					X: 15
					Y: 15
					Height: 31
					Width: 135
					Text: button-bg-system-maps-tab
				<PERSON><PERSON>@REMOTE_MAPS_TAB_BUTTON:
					X: 15
					Y: 15
					Height: 31
					Width: 135
					Text: button-bg-remote-maps-tab
				<PERSON><PERSON>@USER_MAPS_TAB_BUTTON:
					X: 155
					Y: 15
					Height: 31
					Width: 135
					Text: button-bg-user-maps-tab
				Container@MAP_TAB_PANES:
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT - 90
					X: 15
					Y: 45
					Children:
						Container@SYSTEM_MAPS_TAB:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Children:
								ScrollPanel@MAP_LIST:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									ItemSpacing: 1
						Container@REMOTE_MAPS_TAB:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Children:
								ScrollPanel@MAP_LIST:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									ItemSpacing: 1
						Container@USER_MAPS_TAB:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Children:
								ScrollPanel@MAP_LIST:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									ItemSpacing: 1
				ScrollItem@MAP_TEMPLATE:
					Width: 210
					Height: 262
					X: 1
					Visible: false
					EnableChildMouseOver: True
					Children:
						MapPreview@PREVIEW:
							X: (PARENT_WIDTH - WIDTH) / 2
							Y: 3
							Width: 204
							Height: 204
							IgnoreMouseOver: true
							IgnoreMouseInput: true
						LabelWithTooltip@TITLE:
							X: 4
							Y: PARENT_HEIGHT - HEIGHT - 33
							Width: PARENT_WIDTH - 8
							Height: 24
							Align: Center
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
						Label@DETAILS:
							Width: PARENT_WIDTH - 8
							Height: 12
							X: 4
							Y: PARENT_HEIGHT - HEIGHT - 26
							Align: Center
							Font: Tiny
						LabelWithTooltip@AUTHOR:
							Width: PARENT_WIDTH - 8
							Height: 12
							X: 4
							Y: PARENT_HEIGHT - HEIGHT - 14
							Align: Center
							Font: Tiny
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
						Label@SIZE:
							Width: PARENT_WIDTH - 8
							Height: 12
							X: 4
							Y: PARENT_HEIGHT - HEIGHT - 2
							Align: Center
							Font: Tiny
				Container@FILTER_ORDER_CONTROLS:
					X: 15
					Y: PARENT_HEIGHT - 40
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT
					Children:
						Label@FILTER_DESC:
							Width: 40
							Height: 24
							Font: Bold
							Align: Right
							Text: label-filter-order-controls-desc
						TextField@MAPFILTER_INPUT:
							X: 45
							Width: 150
							Height: 25
						Label@FILTER_DESC_JOINER:
							X: 195
							Width: 30
							Height: 24
							Font: Bold
							Align: Center
							Text: label-filter-order-controls-desc-joiner
						DropDownButton@GAMEMODE_FILTER:
							X: 225
							Width: 200
							Height: 25
						Label@ORDERBY_LABEL:
							X: PARENT_WIDTH - WIDTH - 200 - 5
							Width: 100
							Height: 24
							Font: Bold
							Align: Right
							Text: label-filter-order-controls-orderby
						DropDownButton@ORDERBY:
							X: PARENT_WIDTH - WIDTH
							Width: 200
							Height: 25
				Label@REMOTE_MAP_LABEL:
					X: 140
					Y: 539
					Width: PARENT_WIDTH - 430
					Height: 35
					Align: Center
					Font: Bold
				Button@BUTTON_CANCEL:
					Key: escape
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-back
				Button@RANDOMMAP_BUTTON:
					Key: space
					X: PARENT_WIDTH - 150 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-randommap
				Button@DELETE_MAP_BUTTON:
					X: PARENT_WIDTH - 300 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-delete-map
				Button@DELETE_ALL_MAPS_BUTTON:
					X: PARENT_WIDTH - 450 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-delete-all-maps
				Button@BUTTON_OK:
					Key: return
					X: PARENT_WIDTH - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-ok
				TooltipContainer@TOOLTIP_CONTAINER:
