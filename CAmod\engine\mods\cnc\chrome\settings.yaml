Container@SETTINGS_PANEL:
	Logic: SettingsLogic
		ButtonStride: 0, 45
		Panels:
			DISPLAY_PANEL: Display
			AUDIO_PANEL: Audio
			INPUT_PANEL: Input
			HOTKEYS_PANEL: Hotkeys
			ADVANCED_PANEL: Advanced
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 640
	Height: 435
	Children:
		Label@SETTINGS_LABEL_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: button-settings-title
		Button@BACK_BUTTON:
			Key: escape
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-back
		But<PERSON>@RESET_BUTTON:
			X: 150
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-settings-panel-reset
		Container@SETTINGS_TAB_CONTAINER:
			X: 0 - 140 + 1
			Children:
				But<PERSON>@BUTTON_TEMPLATE:
					Width: 140
					Height: 35
		Background@PANEL_CONTAINER:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Container@PANEL_TEMPLATE:
					X: 15
					Y: 15
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT - 30
		TooltipContainer@SETTINGS_TOOLTIP_CONTAINER:
