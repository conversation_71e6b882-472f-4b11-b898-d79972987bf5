explosion:
	Defaults:
		BlendMode: Additive
		Tick: 80
		ZOffset: 511
	piff:
		Filename: DATA.R16
		Start: 3879
		Length: 5
		BlendMode: Alpha
		Alpha: 0.5
	piffs:
		Filename: DATA.R16
		Start: 3682
		Length: 4
	small_explosion:
		Filename: DATA.R16
		Start: 3656
		Length: 15
	med_explosion:
		Filename: DATA.R16
		Start: 3643
		Length: 12
	tiny_explosion:
		Filename: DATA.R16
		Start: 3639
		Length: 4
	nuke:
		Filename: DATA.R16
		Start: 4218
		Length: 14
	self_destruct:
		Filename: DATA.R16
		Start: 3686
		Length: 15
		BlendMode: Alpha
	building:
		Filename: DATA.R16
		Start: 3701
		Length: 22
	large_explosion:
		Filename: DATA.R16
		Start: 4241
		Length: 22
	small_napalm:
		Filename: DATA.R16
		Start: 3674
		Length: 8
	rocket_explosion:
		Filename: DATA.R16
		Start: 3634
		Length: 5
		BlendMode: Alpha
		Alpha: 0.5
	shockwave:
		Filename: DATA.R16
		Start: 3940
		Length: 6
		Tick: 120
	deviator:
		Filename: DATA.R16
		Start: 3765
		Length: 23
		Alpha: 0.5
		BlendMode: Alpha
		Offset: 12, -10
		Tick: 120
		Remap: 54F94B
	corpse:
		Filename: DATA.R16
		ZOffset: -511
		Start: 430
		Length: 12
		Tick: 1600
	wall_explosion:
		Filename: DATA.R16
		Start: 4130
		Length: 8
		Tick: 120
		BlendMode: Alpha
	devastator:
		Filename: DATA.R16
		Start: 3947
		Length: 17
		Tick: 120

large_trail:
	idle:
		Filename: DATA.R16
		Start: 4126
		Length: 4
		Tick: 120
		BlendMode: Additive
		ZOffset: 1023

small_trail:
	idle:
		Filename: DATA.R16
		Start: 3988
		Length: 4
		Tick: 80
		BlendMode: Additive
		ZOffset: 1023
		Alpha: 0.5

small_trail2:
	idle:
		Filename: DATA.R16
		Start: 3793
		Length: 4
		Tick: 80
		BlendMode: Additive
		ZOffset: 1023
		Alpha: 0.5

bazooka_trail:
	idle:
		Filename: DATA.R16
		Start: 3634
		Length: 4
		Tick: 80
		ZOffset: 1023
		Alpha: 0.5

bazooka_trail2:
	idle:
		Filename: DATA.R16
		Start: 3797
		Length: 4
		Tick: 80
		ZOffset: 1023
		Alpha: 0.5

deviator_trail:
	idle:
		Filename: DATA.R16
		Start: 3788
		Length: 5
		Tick: 80
		ZOffset: 1023
		Alpha: 0.5
		Remap: 54F94B

laserfire:
	idle:
		Filename: DATA.R16
		Start: 3639
		Length: 4
		Tick: 80
		BlendMode: Additive
		ZOffset: 511

sandtrail:
	Defaults:
		Length: 8
		Tick: 200
		ZOffset: -512
	traila:
		Filename: sandtrail.shp
	trailb:
		Filename: sandtrail.shp
		Frames: 2, 6, 4, 5, 0, 1, 3, 7
	trailc:
		Filename: sandtrail.shp
		Frames: 7, 4, 6, 5, 2, 0, 3, 1

pips:
	groups:
		Filename: DATA.R16
		Length: 10
		Frames: 18, 19, 20, 21, 22, 23, 24, 25, 26, 17
		Offset: 3, 3
	pickup-indicator:
		Filename: DATA.R16
		Start: 112
		Offset: -9, -9
	pip-empty:
		Filename: DATA.R16
		Start: 15
	pip-green:
		Filename: DATA.R16
		Start: 16
	tag-upgraded:
		Filename: DATA.R16
		Start: 110
		Offset: -8,-8

clock:
	idle:
		Filename: clock.shp
		Length: *

powerdown:
	disabled:
		Filename: speed.shp
		Start: 3
		ZOffset: 2047

poweroff:
	offline:
		Filename: poweroff.shp
		Length: *
		Tick: 160
		ZOffset: 2047

rank:
	rank-veteran-1:
		Filename: rank.shp
	rank-veteran-2:
		Filename: rank.shp
		Start: 1
	rank-veteran-3:
		Filename: rank.shp
		Start: 2
	rank-elite:
		Filename: rank.shp
		Start: 3

overlay:
	Defaults:
		Filename: DATA.R16
		Offset: -16,-16
	build-valid:
	build-unsafe:
		Filename: concfoot.shp
		Offset: 0, 0
	build-invalid:
		Start: 1
	target-select:
		Start: 2
	target-valid:
	target-invalid:
		Start: 1

editor-overlay:
	Defaults:
		Filename: DATA.R16
		Offset: -16,-16
	copy:
	paste:
		Start: 1

rallypoint:
	flag:
		Filename: flagfly.shp
		Length: *
		Offset: 11,-5
		ZOffset: 2535
	circles:
		Filename: fpls.shp
		Length: *
		ZOffset: 2047

beacon:
	arrow:
		Filename: MOUSE.R16
		Start: 148
		Offset: -24,-24
		ZOffset: 2535
	circles:
		Filename: fpls.shp
		Length: *
		ZOffset: 2047

rpg:
	idle:
		Filename: DATA.R16
		Start: 3263
		Facings: -32
		ZOffset: 1023

120mm:
	idle:
		Filename: DATA.R16
		Start: 3262
		BlendMode: Additive
		ZOffset: 1023

155mm:
	idle:
		Filename: DATA.R16
		Start: 3329
		ZOffset: 1023

crate-effects:
	Defaults:
		ZOffset: 2047
	dollar:
		Filename: DATA.R16
		Start: 3932
		Length: 8
	reveal-map:
		Filename: DATA.R16
		Start: 4200
		Length: 18
		Alpha: 0.5
	hide-map:
		Filename: DATA.R16
		Frames: 4217, 4216, 4215, 4214, 4213, 4212, 4211, 4210, 4209, 4208, 4207, 4206, 4205, 4204, 4203, 4202, 4201, 4200
		Length: 18
	levelup:
		Filename: levelup.shp
		Length: *
		Tick: 200
	cloak:
		Filename: DATA.R16
		Start: 4164
		Length: 36

allyrepair:
	repair:
		Filename: DATA.R16
		Frames: 3, 39
		Length: 2
		Tick: 300
		Offset: -11,-10
		ZOffset: 2047

missile:
	idle:
		Filename: DATA.R16
		Start: 3336
		Facings: -32
		ZOffset: 1023

missile2:
	idle:
		Filename: DATA.R16
		Start: 3554
		Facings: -32
		ZOffset: 1023

deathhand:
	up:
		Filename: DATA.R16
		Start: 2147
		ZOffset: 1023
		Offset: 2,0
	down:
		Filename: DATA.R16
		Start: 2148
		ZOffset: 1023
		Offset: -1,0
	icon:
		Filename: DATA.R16
		Start: 4296
		Offset: -30,-24

fire:
	1:
		Filename: DATA.R16
		Start: 3965
		Length: 10
		Offset: 4,-17
		ZOffset: 1023
		Tick: 100
		BlendMode: Additive
	2:
		Filename: DATA.R16
		Start: 3976
		Length: 11
		Offset: 0,-3
		ZOffset: 1023
		Tick: 100
		BlendMode: Additive
	3:
		Filename: DATA.R16
		Start: 4138
		Length: 13
		Offset: 0,-3
		ZOffset: 1023
		Tick: 100
		BlendMode: Additive
	4:
		Filename: DATA.R16
		Start: 3965
		Length: 10
		Offset: 0,-3
		ZOffset: 1023
		Tick: 100
		BlendMode: Additive

smoke_m:
	Defaults:
		ZOffset: 511
		BlendMode: Subtractive
	idle:
		Filename: DATA.R16
		Start: 3671
		Length: 2
	loop:
		Filename: DATA.R16
		Start: 3671
		Length: 2
	end:
		Filename: DATA.R16
		Start: 3671
		Length: 3

smoke3:
	particles:
		Filename: DATA.R16
		ZOffset: 511
		Start: 3747
		Length: 7
		Tick: 120
		BlendMode: Subtractive

bombs:
	idle:
		Filename: DATA.R16
		Start: 3528
		Length: 4
		ZOffset: 1023

grenade:
	idle:
		Filename: DATA.R16
		Start: 3618
		Length: 4
		Tick: 80
		ZOffset: 1023

shrapnel:
	idle:
		Filename: DATA.R16
		Start: 3538
		Length: 4
		ZOffset: 1023

shrapnel2:
	idle:
		Filename: DATA.R16
		Start: 3542
		Length: 1
		ZOffset: 1023

shrapnel3:
	idle:
		Filename: DATA.R16
		Start: 3543
		Length: 8
		ZOffset: 1023

shrapnel4:
	idle:
		Filename: DATA.R16
		Start: 3551
		Length: 1
		ZOffset: 1023

mpspawn:
	idle:
		Filename: mpspawn.shp
		Length: *

waypoint:
	idle:
		Filename: waypoint.shp
		Length: *

camera:
	idle:
		Filename: camera.shp
		Length: *

wormspawner:
	idle:
		Filename: wormspawner.shp
		Length: *

sietch:
	idle:
		Filename: DATA.R16
		Start: 3246
		Offset: -32,32

doubleblast:
	idle:
		Filename: DATA.R16
		Start: 3527
		Facings: -16
		BlendMode: Additive
		ZOffset: 511

doubleblastbullet:
	idle:
		Filename: DATA.R16
		Start: 3496
		Facings: -16
		BlendMode: Additive
		ZOffset: 1023

icon:
	ornistrike:
		Filename: DATA.R16
		Start: 4292
		Offset: -30,-24
	fremen:
		Filename: DATA.R16
		Start: 4293
		Offset: -30,-24
	saboteur:
		Filename: DATA.R16
		Start: 4295
		Offset: -30,-24
	deathhand:
		Filename: DATA.R16
		Start: 4296
		Offset: -30,-24

crate:
	idle:
		Filename: DATA.R16
		Start: 102
		ZOffset: -511
		Offset: -16,-16

spicebloom:
	grow0:
		Filename: DATA.R16
		Start: 106
		Length: 1
		ZOffset: -1023
		Offset: -16,-16
	grow1:
		Filename: DATA.R16
		Start: 107
		Length: 1
		ZOffset: -1023
		Offset: -16,-16
	grow2:
		Filename: DATA.R16
		Start: 108
		Length: 1
		ZOffset: -1023
		Offset: -16,-16
	grow3:
		Filename: DATA.R16
		Start: 109
		Length: 1
		ZOffset: -1023
		Offset: -16,-16
	spurt:
		Filename: DATA.R16
		Start: 4233
		Length: 8
		Tick: 80
		BlendMode: Additive
		ZOffset: 511
		Offset: 0, -16

spicebloom.editor:
	idle:
		Filename: DATA.R16
		Start: 109
		Length: 1
		ZOffset: -1023
		Offset: -16,-16
		Alpha: 0.5

moveflsh:
	idle:
		Filename: DATA.R16
		Start: 3874
		Length: 5
		Tick: 80
		BlendMode: Subtractive
		ZOffset: 2047

resources:
	spicea:
		Filename: BLOXBASE.R16
		Frames: 748, 300, 750, 751, 752, 753, 754, 755, 756, 757, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 300
		Length: 51
		Offset: -16,-16
	spiceb:
		Filename: BLOXBASE.R16
		Frames: 749, 301, 750, 751, 752, 753, 754, 755, 756, 757, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 301
		Length: 51
		Offset: -16,-16
	spicec:
		Filename: BLOXBASE.R16
		Frames: 793, 320, 750, 751, 752, 753, 754, 755, 756, 757, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 320
		Length: 51
		Offset: -16,-16
	spiced:
		Filename: BLOXBASE.R16
		Frames: 748, 321, 750, 751, 752, 753, 754, 755, 756, 757, 760, 761, 762, 763, 764, 765, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 776, 777, 778, 779, 780, 781, 782, 783, 784, 785, 786, 787, 788, 789, 790, 791, 792, 793, 794, 795, 796, 797, 798, 799, 321
		Length: 51
		Offset: -16,-16

shroud:
	Defaults:
		Filename: DATA.R16
		BlendMode: Subtractive
		UseShadow: false
		Offset: -16,-16
		Length: 14
	shrouda:
		Start: 40
	shroudb:
		Start: 56
	shroudc:
		Start: 72
	shroudd:
		Start: 88
	shroudfull:
		Filename: shroud.png
		Length: 1
	foga:
		Start: 40
		ConvertShroudToFog: true
		BlendMode: Multiply
	fogb:
		Start: 56
		ConvertShroudToFog: true
		BlendMode: Multiply
	fogc:
		Start: 72
		ConvertShroudToFog: true
		BlendMode: Multiply
	fogd:
		Start: 88
		ConvertShroudToFog: true
		BlendMode: Multiply
	fogfull:
		Filename: fog.png
		Length: 1
		BlendMode: Multiply

rockcraters:
	rockcrater1:
		Filename: DATA.R16
		Start: 114
		Length: 16
		Offset: -16,-16
	rockcrater2:
		Filename: DATA.R16
		Start: 130
		Length: 16
		Offset: -16,-16

sandcraters:
	sandcrater1:
		Filename: DATA.R16
		Start: 146
		Length: 16
		Offset: -16,-16
	sandcrater2:
		Filename: DATA.R16
		Start: 162
		Length: 16
		Offset: -16,-16

ornidirection:
	arrow-t:
		Filename: MOUSE.R16
		Start: 112
		Offset: -25, -53, 0
	arrow-tr:
		Filename: MOUSE.R16
		Start: 120
		Offset: 6, -47, 0
	arrow-r:
		Filename: MOUSE.R16
		Start: 128
		Offset: 17, -26, 0
	arrow-br:
		Filename: MOUSE.R16
		Start: 136
		Offset: 6, -1, 0
	arrow-b:
		Filename: MOUSE.R16
		Start: 148
		Offset: -25, 7, 0
	arrow-bl:
		Filename: MOUSE.R16
		Start: 156
		Offset: -52, -3, 0
	arrow-l:
		Filename: MOUSE.R16
		Start: 164
		Offset: -61, -26, 0
	arrow-tl:
		Filename: MOUSE.R16
		Start: 172
		Offset: -52, -44, 0

null:
	idle:
		Filename: DATA.R16
		Start: 3552
