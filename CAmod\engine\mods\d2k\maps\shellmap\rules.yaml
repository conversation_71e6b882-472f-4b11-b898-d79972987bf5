Player:
	-ConquestVictoryConditions:
	-HarvesterInsurance:
	PlayerResources:
		ResourceValues:
			Spice: 0

World:
	-CrateSpawner:
	-SpawnStartingUnits:
	-MapStartingLocations:
	ActorSpawnManager:
		Minimum: 1
		Maximum: 3
	MusicPlaylist:
		BackgroundMusic: options
		AllowMuteBackgroundMusic: true
		DisableWorldSounds: true
	LuaScript:
		Scripts: utils.lua, d2k-shellmap.lua

^ExistsInWorld:
	-GivesExperience:

^Building:
	DamageMultiplier@UNKILLABLE:
		Modifier: 0

^Vehicle:
	WithDecoration@CARRYALL:
		RequiresSelection: true

wall:
	DamageMultiplier@UNKILLABLE:
		Modifier: 0

upgrade.conyard:
	Valued:
		Cost: 0

upgrade.barracks:
	Valued:
		Cost: 0

upgrade.light:
	Valued:
		Cost: 0

upgrade.heavy:
	Valued:
		Cost: 0

upgrade.hightech:
	Valued:
		Cost: 0

light_inf:
	Valued:
		Cost: 0

trooper:
	Valued:
		Cost: 0

grenadier:
	Valued:
		Cost: 0

sardaukar:
	Buildable:
		Prerequisites: ~player.corrino
	Valued:
		Cost: 0

mpsardaukar:
	Valued:
		Cost: 0

trike:
	Valued:
		Cost: 0

raider:
	Valued:
		Cost: 0

stealth_raider:
	Valued:
		Cost: 0

quad:
	Valued:
		Cost: 0

^combat_tank:
	Valued:
		Cost: 0

siege_tank:
	Valued:
		Cost: 0

missile_tank:
	Valued:
		Cost: 0

carryall.reinforce:
	Cargo:
		MaxWeight: 10

barracks:
	ProvidesPrerequisite@harkonnen:
		Prerequisite: barracks.harkonnen
		Factions: harkonnen, corrino

light_factory:
	ProvidesPrerequisite@harkonnen:
		Prerequisite: light.harkonnen
		Factions: harkonnen, corrino
	ProvidesPrerequisite@trikes:
		Prerequisite: light.regulartrikes
		Factions: atreides, harkonnen, corrino

heavy_factory:
	ProvidesPrerequisite@harkonnen:
		Prerequisite: heavy.harkonnen
		Factions: harkonnen, corrino
	ProvidesPrerequisite@missiletank:
		Prerequisite: heavy.missiletank
		Factions: atreides, harkonnen, corrino

starport:
	ProvidesPrerequisite@harkonnen:
		Prerequisite: starport.harkonnen
		Factions: harkonnen, corrino
