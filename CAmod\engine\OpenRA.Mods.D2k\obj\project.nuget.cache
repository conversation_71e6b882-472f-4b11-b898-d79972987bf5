{"version": 2, "dgSpecHash": "6hNFzQ14e785g5VItguO0w/F4bnw80PaprjaH0mgvPoIRMbW5LiADXA5qAAF1ZhIeVSw/Stwmuz6z+vVvZMOmA==", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Mods.D2k\\OpenRA.Mods.D2k.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\discordrichpresence\\1.2.1.24\\discordrichpresence.1.2.1.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\linguini.bundle\\0.8.1\\linguini.bundle.0.8.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\linguini.shared\\0.8.0\\linguini.shared.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\linguini.syntax\\0.8.0\\linguini.syntax.0.8.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencymodel\\6.0.2\\microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.platforms\\5.0.0\\microsoft.netcore.platforms.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.registry\\5.0.0\\microsoft.win32.registry.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mono.nat\\3.0.4\\mono.nat.3.0.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mp3sharp\\1.0.5\\mp3sharp.1.0.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.1\\newtonsoft.json.13.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nuget.commandline\\6.12.1\\nuget.commandline.6.12.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\nvorbis\\0.10.5\\nvorbis.0.10.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-eluant\\1.0.22\\openra-eluant.1.0.22.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\openra-fuzzylogiclibrary\\1.0.1\\openra-fuzzylogiclibrary.1.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pfim\\0.11.3\\pfim.0.11.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\rix0rrr.beaconlib\\1.0.2\\rix0rrr.beaconlib.1.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\roslynator.analyzers\\4.2.0\\roslynator.analyzers.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\roslynator.formatting.analyzers\\4.2.0\\roslynator.formatting.analyzers.4.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\sharpziplib\\1.4.2\\sharpziplib.1.4.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stylecop.analyzers\\1.2.0-beta.435\\stylecop.analyzers.1.2.0-beta.435.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\stylecop.analyzers.unstable\\1.2.0.435\\stylecop.analyzers.unstable.1.2.0.435.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.io\\4.3.0\\system.io.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection\\4.3.0\\system.reflection.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.reflection.primitives\\4.3.0\\system.reflection.primitives.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.loader\\4.3.0\\system.runtime.loader.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.accesscontrol\\5.0.0\\system.security.accesscontrol.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.encodings.web\\6.0.1\\system.text.encodings.web.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.text.json\\6.0.11\\system.text.json.6.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.channels\\6.0.0\\system.threading.channels.6.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.threading.tasks\\4.3.0\\system.threading.tasks.4.3.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.valuetuple\\4.5.0\\system.valuetuple.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\taglibsharp\\2.3.0\\taglibsharp.2.3.0.nupkg.sha512"], "logs": []}