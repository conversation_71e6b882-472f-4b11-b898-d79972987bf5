name: Release Packaging

on:
  push:
    tags:
    - 'release-*'
    - 'playtest-*'
    - 'devtest-*'

permissions:
  contents: write  #  for release creation (svenstaro/upload-release-action)

jobs:
  source:
    name: Source Tarball
    runs-on: ubuntu-22.04
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Prepare Environment
        run: echo "GIT_TAG=${GITHUB_REF#refs/tags/}" >> ${GITHUB_ENV}

      - name: Package Source
        run: |
          mkdir -p build/source
          ./packaging/source/buildpackage.sh "${GIT_TAG}" "${PWD}/build/source"

      - name: Upload Packages
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          tag: ${{ github.ref }}
          overwrite: true
          file_glob: true
          file: build/source/*

  linux:
    name: Linux AppImages
    runs-on: ubuntu-22.04
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'

      - name: Prepare Environment
        run: echo "GIT_TAG=${GITHUB_REF#refs/tags/}" >> ${GITHUB_ENV}

      - name: Package AppImages
        run: |
          mkdir -p build/linux
          sudo apt-get install -y desktop-file-utils
          ./packaging/linux/buildpackage.sh "${GIT_TAG}" "${PWD}/build/linux"

      - name: Upload Packages
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash
        run: |
          gh release upload ${{ github.ref_name }} build/linux/*

  macos:
    name: macOS Disk Image
    runs-on: macos-13
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'

      - name: Prepare Environment
        run: echo "GIT_TAG=${GITHUB_REF#refs/tags/}" >> ${GITHUB_ENV}

      - name: Package Disk Image
        env:
          MACOS_DEVELOPER_IDENTITY: ${{ secrets.MACOS_DEVELOPER_IDENTITY }}
          MACOS_DEVELOPER_CERTIFICATE_BASE64: ${{ secrets.MACOS_DEVELOPER_CERTIFICATE_BASE64 }}
          MACOS_DEVELOPER_CERTIFICATE_PASSWORD: ${{ secrets.MACOS_DEVELOPER_CERTIFICATE_PASSWORD }}
          MACOS_DEVELOPER_USERNAME: ${{ secrets.MACOS_DEVELOPER_USERNAME }}
          MACOS_DEVELOPER_PASSWORD: ${{ secrets.MACOS_DEVELOPER_PASSWORD }}
        run: |
          mkdir -p build/macos
          ./packaging/macos/buildpackage.sh "${GIT_TAG}" "${PWD}/build/macos"

      - name: Upload Package
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash
        run: |
          gh release upload ${{ github.ref_name }} build/macos/*

  windows:
    name: Windows Installers
    runs-on: ubuntu-22.04
    steps:
      - name: Clone Repository
        uses: actions/checkout@v4

      - name: Install .NET 6.0
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '6.0.x'

      - name: Prepare Environment
        run: |
          echo "GIT_TAG=${GITHUB_REF#refs/tags/}" >> ${GITHUB_ENV}
          sudo apt-get update
          sudo apt-get install nsis wine64

      - name: Package Installers
        run: |
          mkdir -p build/windows
          ./packaging/windows/buildpackage.sh "${GIT_TAG}" "${PWD}/build/windows"

      - name: Upload Packages
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        shell: bash
        run: |
          gh release upload ${{ github.ref_name }} build/windows/*
