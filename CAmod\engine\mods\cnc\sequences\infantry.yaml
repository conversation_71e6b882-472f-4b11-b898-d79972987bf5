vice:
	idle:
		Filename: vice.shp
		Length: *
	muzzle:
		Combine:
			0:
				Filename: chem-n.shp
				Length: *
				Offset: 1,2
			1:
				Filename: chem-nw.shp
				Length: *
				Offset: 8,2
			2:
				Filename: chem-w.shp
				Length: *
				Offset: 8,-3
			3:
				Filename: chem-sw.shp
				Length: *
				Offset: 7,-6
			4:
				Filename: chem-s.shp
				Length: *
				Offset: 1,-6
			5:
				Filename: chem-se.shp
				Length: *
				Offset: -5,-6
			6:
				Filename: chem-e.shp
				Length: *
				Offset: -7,-3
			7:
				Filename: chem-ne.shp
				Length: *
				Offset: -3,2
		Facings: 8
		Length: 13
	die:
		Filename: chemball.shp
		Length: *
		ZOffset: 2047
	icon:
		Filename: viceicnh.shp

pvice:
	Inherits: vice
	Defaults:
		Filename: pvice.shp

e1:
	Defaults:
		Filename: e1.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	shoot:
		Start: 64
		Length: 8
		Facings: 8
	prone-stand:
		Start: 144
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 144
		Stride: 4
		Facings: 8
	prone-run:
		Start: 144
		Length: 4
		Facings: 8
		Tick: 100
	# stand -> prone transition
	liedown:
		Start: 128
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 176
		Length: 2
		Facings: 8
	prone-shoot:
		Start: 192
		Length: 8
		Facings: 8
	idle1:
		Start: 257
		Length: 15
		Tick: 120
	idle2:
		Start: 272
		Length: 16
		Tick: 120
	idle3:
		Start: 289
		Length: 22
		Tick: 120
	cheer:
		Start: 460
		Length: 3
		Facings: 8
		Tick: 120
	# Dancing
	idle4:
		Start: 517
		Length: 9
		Tick: 120
	# Shot
	die1:
		Start: 381
		Length: 9
		Tick: 80
	# Explode
	die2:
		Start: 390
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 398
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 406
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 418
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 366
		Length: 11
		Tick: 80
	die-crushed:
		Filename: e1rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	icon:
		Filename: e1icnh.tem

e2:
	Defaults:
		Filename: e2.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	throw:
		Start: 64
		Length: 20
		Facings: 8
	# stand -> prone transition
	liedown:
		Start: 224
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 272
		Length: 2
		Facings: 8
	prone-stand:
		Start: 240
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 240
		Stride: 4
		Facings: 8
	prone-run:
		Start: 240
		Length: 4
		Facings: 8
		Tick: 100
	prone-throw:
		Start: 288
		Length: 12
		Facings: 8
	idle1:
		Start: 384
		Length: 16
		Tick: 120
	idle2:
		Start: 400
		Length: 13
		Tick: 120
	cheer:
		Start: 588
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 509
		Length: 9
		Tick: 80
	# Explode
	die2:
		Start: 518
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 526
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 534
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 546
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 494
		Length: 11
		Tick: 80
	die-crushed:
		Filename: e2rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	icon:
		Filename: e2icnh.tem

e3:
	Defaults:
		Filename: e3.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	shoot:
		Start: 64
		Length: 8
		Facings: 8
	# stand -> prone transition
	liedown:
		Start: 128
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 176
		Length: 2
		Facings: 8
	prone-stand:
		Start: 144
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 144
		Stride: 4
		Facings: 8
	prone-run:
		Start: 144
		Length: 4
		Facings: 8
		Tick: 100
	prone-shoot:
		Start: 192
		Length: 10
		Facings: 8
	idle1:
		Start: 274
		Length: 12
		Tick: 120
	idle2:
		Start: 289
		Length: 14
		Tick: 120
	cheer:
		Start: 476
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 397
		Length: 9
		Tick: 80
	# Explode
	die2:
		Start: 406
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 414
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 422
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 434
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 382
		Length: 11
		Tick: 80
	die-crushed:
		Filename: e3rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	icon:
		Filename: e3icnh.tem

e4:
	Defaults:
		Filename: e4.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	shoot:
		Start: 64
		Length: 16
		Facings: 8
	# stand -> prone transition
	liedown:
		Start: 192
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 240
		Length: 2
		Facings: 8
	prone-stand:
		Start: 208
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 208
		Stride: 4
		Facings: 8
	prone-run:
		Start: 208
		Length: 4
		Facings: 8
		Tick: 100
	prone-shoot:
		Start: 256
		Length: 16
		Facings: 8
	idle1:
		Start: 384
		Length: 16
		Tick: 120
	idle2:
		Start: 400
		Length: 16
		Tick: 120
	cheer:
		Start: 588
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 509
		Length: 9
		Tick: 80
	# Explode
	die2:
		Start: 518
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 526
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 534
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 546
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 494
		Length: 10
		Tick: 80
	die-crushed:
		Filename: e4rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	muzzle:
		Filename:
		Combine:
			0:
				Filename: flame-n.shp
				Length: *
				Offset: 1,6
			1:
				Filename: flame-nw.shp
				Length: *
				Offset: 8,7
			2:
				Filename: flame-w.shp
				Length: *
				Offset: 8,2
			3:
				Filename: flame-sw.shp
				Length: *
				Offset: 7,-2
			4:
				Filename: flame-s.shp
				Length: *
				Offset: 1,-2
			5:
				Filename: flame-se.shp
				Length: *
				Offset: -5,-2
			6:
				Filename: flame-e.shp
				Length: *
				Offset: -7,2
			7:
				Filename: flame-ne.shp
				Length: *
				Offset: -7,8
		Facings: 8
		Length: 13
	icon:
		Filename: e4icnh.tem

e5:
	Defaults:
		Filename: e5.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	shoot:
		Start: 64
		Length: 16
		Facings: 8
	# stand -> prone transition
	liedown:
		Start: 192
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 240
		Length: 2
		Facings: 8
	prone-stand:
		Start: 208
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 208
		Stride: 4
		Facings: 8
	prone-run:
		Start: 208
		Length: 4
		Facings: 8
		Tick: 100
	prone-shoot:
		Start: 256
		Length: 16
		Facings: 8
	idle1:
		Start: 384
		Length: 16
		Tick: 120
	idle2:
		Start: 400
		Length: 16
		Tick: 120
	cheer:
		Start: 588
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 509
		Length: 9
		Tick: 80
	# Explode
	die2:
		Start: 518
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 526
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 534
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 546
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 494
		Length: 10
		Tick: 80
	die-crushed:
		Filename: e4rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	muzzle:
		Filename:
		Combine:
			0:
				Filename: chem-n.shp
				Length: *
				Offset: 1,2
			1:
				Filename: chem-nw.shp
				Length: *
				Offset: 8,2
			2:
				Filename: chem-w.shp
				Length: *
				Offset: 8,-3
			3:
				Filename: chem-sw.shp
				Length: *
				Offset: 7,-6
			4:
				Filename: chem-s.shp
				Length: *
				Offset: 1,-6
			5:
				Filename: chem-se.shp
				Length: *
				Offset: -5,-6
			6:
				Filename: chem-e.shp
				Length: *
				Offset: -7,-3
			7:
				Filename: chem-ne.shp
				Length: *
				Offset: -3,2
		Facings: 8
		Length: 13
	icon:
		Filename: e5icnh.tem

e6:
	Defaults:
		Filename: e6.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	# stand -> prone transition
	liedown:
		Start: 66
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 114
		Length: 2
		Facings: 8
	prone-stand:
		Start: 82
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 82
		Stride: 4
		Facings: 8
	prone-run:
		Start: 82
		Length: 4
		Facings: 8
		Tick: 100
	idle1:
		Start: 114
		Length: 6
		Tick: 120
	idle2:
		Start: 200
		Length: 6
		Tick: 120
	cheer:
		Start: 200
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 146
		Length: 8
		Tick: 80
	# Explode
	die2:
		Start: 154
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 162
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 170
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 182
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 130
		Length: 4
		Tick: 80
	die-crushed:
		Filename: e1rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	icon:
		Filename: e6icnh.tem

rmbo:
	Defaults:
		Filename: rmbo.shp
	stand:
		Facings: 8
	stand2:
		Start: 8
		Facings: 8
	run:
		Start: 16
		Length: 6
		Facings: 8
		Tick: 100
	shoot:
		Start: 64
		Length: 4
		Facings: 8
	# stand -> prone transition
	liedown:
		Start: 96
		Length: 2
		Facings: 8
	# prone -> stand transition
	standup:
		Start: 144
		Length: 2
		Facings: 8
	prone-stand:
		Start: 112
		Stride: 4
		Facings: 8
	prone-stand2:
		Start: 112
		Stride: 4
		Facings: 8
	prone-run:
		Start: 112
		Length: 4
		Facings: 8
		Tick: 100
	prone-shoot:
		Start: 160
		Length: 4
		Facings: 8
	idle1:
		Start: 192
		Length: 16
		Tick: 120
	idle2:
		Start: 208
		Length: 16
		Tick: 120
	idle3:
		Start: 224
		Length: 15
		Tick: 120
	cheer:
		Start: 396
		Length: 3
		Facings: 8
		Tick: 120
	# Shot
	die1:
		Start: 318
		Length: 8
		Tick: 80
	# Explode
	die2:
		Start: 326
		Length: 8
		Tick: 80
	# Fly and explode in air
	die3:
		Start: 334
		Length: 8
		Tick: 80
	# Fly through air squish on ground
	die4:
		Start: 342
		Length: 12
		Tick: 80
	# Burn
	die5:
		Start: 354
		Length: 18
		Tick: 80
	# Tib
	die6:
		Start: 308
		Length: 4
		Tick: 80
	die-crushed:
		Filename: e1rot.shp
		Start: 16
		Length: 4
		Tick: 1600
		ZOffset: -511
	icon:
		Filename: rmboicnh.tem
