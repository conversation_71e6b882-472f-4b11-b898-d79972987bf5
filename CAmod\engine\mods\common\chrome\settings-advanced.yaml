Container@ADVANCED_PANEL:
	Logic: AdvancedSettingsLogic
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		ScrollPanel@SETTINGS_SCROLLPANEL:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			CollapseHiddenChildren: True
			TopBottomSpacing: 5
			ItemSpacing: 10
			Children:
				Background@NETWORK_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: label-network-section-header
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@NAT_DISCOVERY_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@NAT_DISCOVERY:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-nat-discovery-container
						Container@FETCH_NEWS_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@FETCH_NEWS_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-fetch-news-container
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@PERFGRAPH_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@PERFGRAPH_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-perfgraph-container
						Container@CHECK_VERSION_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@CHECK_VERSION_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-check-version-container
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@PERFTEXT_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@PERFTEXT_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-perftext-container
						Container@SENDSYSINFO_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@SENDSYSINFO_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-sendsysinfo-container
								Label@SENDSYSINFO_DESC:
									Y: 30
									Width: PARENT_WIDTH
									Height: 30
									Font: Tiny
									WordWrap: True
									Text: label-sendsysinfo-checkbox-container-desc
				Container@SPACER:
				Background@DEBUG_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: label-debug-section-header
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 40
					Children:
						Container@DEBUG_HIDDEN_CONTAINER:
							X: 10
							Width: PARENT_WIDTH - 10
							Children:
								Label@A:
									Width: PARENT_WIDTH
									Height: 20
									Text: label-debug-hidden-container-a
									Align: Center
								Label@B:
									Y: 20
									Width: PARENT_WIDTH
									Height: 20
									Text: label-debug-hidden-container-b
									Align: Center
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@BOTDEBUG_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@BOTDEBUG_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-botdebug-container
						Container@CHECKBOTSYNC_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@CHECKBOTSYNC_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-checkbotsync-container
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@LUADEBUG_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@LUADEBUG_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-luadebug-container
						Container@CHECKUNSYNCED_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@CHECKUNSYNCED_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-checkunsynced-container
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@REPLAY_COMMANDS_CHECKBOX_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@REPLAY_COMMANDS_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-replay-commands-container
						Container@PERFLOGGING_CHECKBOX_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@PERFLOGGING_CHECKBOX:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-perflogging-container
