^BallisticWeapon:
	ReloadDelay: 40
	Range: 4c768
	Report: tnkfire6.aud
	Projectile: Bullet
		Image: 120MM
		Speed: 682
		Shadow: true
	Warhead@1Dam: SpreadDamage
		Spread: 128
		Damage: 4000
		Versus:
			None: 25
			Wood: 100
			Light: 100
			Heavy: 100
		DamageTypes: Prone50P<PERSON>cent, <PERSON><PERSON><PERSON><PERSON>, ExplosionDeath
	Warhead@2Smu: LeaveSmudge
		SmudgeType: Crater
		InvalidTargets: Vehicle, Structure, Wall, Husk, Trees, Creep
	Warhead@3Eff: CreateEffect
		Explosions: small_frag
		ImpactSounds: xplos.aud
		ImpactActors: false

70mm:
	Inherits: ^BallisticWeapon
	ReloadDelay: 30
	Range: 4c0
	Report: tnkfire3.aud
	Projectile: Bullet
		Speed: 853
	Warhead@1Dam: SpreadDamage
		Damage: 2500
		Versus:
			None: 24
			Wood: 88
			Light: 100
			Heavy: 88
			Concrete: 88

120mm:
	Inherits: ^BallisticWeapon
	Report: tnkfire4.aud

120mmDual:
	Inherits: ^BallisticWeapon
	Burst: 2
	BurstDelays: 8

TurretGun:
	Inherits: ^BallisticWeapon
	ReloadDelay: 20
	Range: 6c0
	Projectile: Bullet
		Speed: 853
	Warhead@1Dam: SpreadDamage
		Versus:
			None: 20
			Wood: 50
			Light: 100
			Heavy: 100

ArtilleryShell:
	Inherits: ^BallisticWeapon
	ReloadDelay: 65
	Range: 11c0
	MinRange: 3c0
	Report: tnkfire2.aud
	TargetActorCenter: true
	Projectile: Bullet
		Speed: 204
		Blockable: false
		LaunchAngle: 56
		Inaccuracy: 1c256
		ContrailLength: 30
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 10000
		Versus:
			None: 140
			Wood: 100
			Light: 112
			Heavy: 75
			Concrete: 75
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@3Eff: CreateEffect
		Explosions: poof
		ImpactSounds: xplosml2.aud

Grenade:
	Inherits: ^BallisticWeapon
	ReloadDelay: 50
	Range: 4c0
	Report: toss1.aud
	Projectile: Bullet
		Speed: 140
		Blockable: false
		LaunchAngle: 62
		Inaccuracy: 813
		Image: BOMB
	Warhead@1Dam: SpreadDamage
		Spread: 341
		Damage: 5000
		Versus:
			None: 100
			Wood: 50
			Light: 80
			Heavy: 34
			Concrete: 50
		DamageTypes: Prone50Percent, TriggerProne, SmallExplosionDeath
	Warhead@3Eff: CreateEffect
		Explosions: small_poof
