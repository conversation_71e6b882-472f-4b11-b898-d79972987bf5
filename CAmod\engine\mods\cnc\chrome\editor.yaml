Container@NEW_MAP_BG:
	Logic: NewMapLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 300
	Height: 95
	Children:
		Label@TITLE:
			Text: label-new-map-bg-title
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@TILESET_LABEL:
					X: 25
					Y: 15
					Width: 95
					Height: 25
					Align: Right
					Text: label-bg-tileset
				DropDownButton@TILESET:
					X: 125
					Y: 15
					Width: 160
					Height: 25
				Label@WIDTH_LABEL:
					X: 25
					Y: 50
					Width: 95
					Height: 25
					Align: Right
					Text: label-bg-width
				TextField@WIDTH:
					X: 125
					Y: 50
					Width: 50
					MaxLength: 3
					Height: 25
					Text: 128
					Type: Integer
				Label@HEIGHT_LABEL:
					X: 135
					Y: 50
					Width: 95
					Height: 25
					Align: Right
					Text: label-bg-height
				TextField@HEIGHT:
					X: 235
					Y: 50
					Width: 50
					MaxLength: 3
					Height: 25
					Text: 128
					Type: Integer
		Button@CANCEL_BUTTON:
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-cancel
			Font: Bold
			Key: escape
		Button@CREATE_BUTTON:
			X: PARENT_WIDTH - WIDTH
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-new-map-bg-create
			Font: Bold
			Key: return

Container@SAVE_MAP_PANEL:
	Logic: SaveMapLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 345
	Height: 195
	Children:
		Label@LABEL_TITLE:
			Text: label-save-map-panel-title
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Background@SAVE_MAP_BACKGROUND:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Label@TITLE_LABEL:
					X: 10
					Y: 15
					Width: 95
					Height: 25
					Align: Right
					Text: label-save-map-background-title
				TextField@TITLE:
					X: 110
					Y: 15
					Width: 220
					MaxLength: 50
					Height: 25
				Label@AUTHOR_LABEL:
					X: 10
					Y: 50
					Width: 95
					Height: 25
					Align: Right
					Text: label-save-map-background-author
				TextField@AUTHOR:
					X: 110
					Y: 50
					Width: 220
					MaxLength: 50
					Height: 25
				Label@VISIBILITY_LABEL:
					X: 10
					Y: 85
					Width: 95
					Height: 25
					Align: Right
					Text: label-save-map-background-visibility
				DropDownButton@VISIBILITY_DROPDOWN:
					X: 110
					Y: 85
					Width: 220
					Height: 25
					Text: dropdownbutton-save-map-background-visibility-dropdown
					Font: Regular
				Label@DIRECTORY_LABEL:
					X: 10
					Y: 120
					Width: 95
					Height: 25
					Align: Right
					Text: label-save-map-background-directory
				DropDownButton@DIRECTORY_DROPDOWN:
					X: 110
					Y: 120
					Width: 220
					Height: 25
					Font: Regular
				Label@FILENAME_LABEL:
					X: 10
					Y: 155
					Width: 95
					Height: 25
					Align: Right
					Text: label-save-map-background-filename
				TextField@FILENAME:
					X: 110
					Y: 155
					Width: 105
					Height: 25
					Type: Filename
				DropDownButton@TYPE_DROPDOWN:
					X: 220
					Y: 155
					Width: 110
					Height: 25
					Font: Regular
		Button@BACK_BUTTON:
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-cancel
			Font: Bold
			Key: escape
		Button@SAVE_BUTTON:
			X: PARENT_WIDTH - 140
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-save-map-panel
			Font: Bold

ScrollPanel@MAP_SAVE_VISIBILITY_PANEL:
	TopBottomSpacing: 5
	ItemSpacing: 5
	Width: 220
	Height: 55
	Children:
		Checkbox@VISIBILITY_TEMPLATE:
			X: 5
			Width: PARENT_WIDTH - 29
			Height: 20
			Font: Regular

Container@EDITOR_ROOT:
	Logic: LoadMapEditorLogic
	Children:
		LogicKeyListener@GLOBAL_KEYHANDLER:
			Logic: MusicHotkeyLogic, ScreenshotHotkeyLogic, MuteHotkeyLogic, EditorQuickSaveHotkeyLogic
				StopMusicKey: StopMusic
				PauseMusicKey: PauseMusic
				PrevMusicKey: PrevMusic
				NextMusicKey: NextMusic
				TakeScreenshotKey: TakeScreenshot
				MuteAudioKey: ToggleMute
				QuickSaveKey: EditorQuickSave
		LogicKeyListener@WORLD_KEYHANDLER:
			Logic: ResetZoomHotkeyLogic
				ResetZoomKey: ResetZoom
		Container@WORLD_ROOT:
		Container@MENU_ROOT:
		TooltipContainer@TOOLTIP_CONTAINER:

Container@EDITOR_WORLD_ROOT:
	Logic: LoadIngamePerfLogic, MapEditorLogic, ActorEditLogic, MapOverlaysLogic, MapEditorSelectionLogic
		ToggleGridOverlayKey: EditorToggleGridOverlay
		ToggleBuildableOverlayKey: EditorToggleBuildableOverlay
		ToggleMarkerOverlayKey: EditorToggleMarkerOverlay
	Children:
		LogicKeyListener@OVERLAY_KEYHANDLER:
		Container@PERF_ROOT:
		EditorViewportController@MAP_EDITOR:
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
			TooltipContainer: TOOLTIP_CONTAINER
			TooltipTemplate: SIMPLE_TOOLTIP
		ViewportController:
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
			IgnoreMouseOver: True
			ZoomInKey: ZoomIn
			ZoomOutKey: ZoomOut
			ScrollUpKey: MapScrollUp
			ScrollDownKey: MapScrollDown
			ScrollLeftKey: MapScrollLeft
			ScrollRightKey: MapScrollRight
			JumpToTopEdgeKey: MapJumpToTopEdge
			JumpToBottomEdgeKey: MapJumpToBottomEdge
			JumpToLeftEdgeKey: MapJumpToLeftEdge
			JumpToRightEdgeKey: MapJumpToRightEdge
			BookmarkSaveKeyPrefix: MapBookmarkSave
			BookmarkRestoreKeyPrefix: MapBookmarkRestore
			BookmarkKeyCount: 4
		Background@RADAR_BG:
			X: WINDOW_WIDTH - 295
			Y: 5
			Width: 290
			Height: 290
			Background: panel-gray
			Children:
				Radar@INGAME_RADAR:
					X: 1
					Y: 1
					Width: PARENT_WIDTH - 2
					Height: PARENT_HEIGHT - 2
		MenuButton@OPTIONS_BUTTON:
			Logic: MenuButtonsChromeLogic
			Key: escape
			X: WINDOW_WIDTH - 294 - WIDTH
			Y: 5
			Width: 30
			Height: 25
			TooltipText: button-editor-world-root-options-tooltip
			TooltipContainer: TOOLTIP_CONTAINER
			Children:
				Image:
					X: 7
					Y: 5
					ImageCollection: order-icons
					ImageName: options
		Background@TOOLS_BG:
			X: WINDOW_WIDTH - 295
			Y: 330
			Width: 290
			Height: WINDOW_HEIGHT - 422
		Container@TILE_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Logic: TileSelectorLogic
			Children:
				Container@TILES_BG:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Children:
						Background:
							Width: 61
							Height: 50
							Background: panel-black
							Children:
								Label@SEARCH_LABEL:
									Y: 1
									Width: PARENT_WIDTH - 5
									Height: 25
									Text: label-tiles-bg-search
									Align: Right
									Font: TinyBold
								Label@CATEGORIES_LABEL:
									Y: 25
									Width: PARENT_WIDTH - 5
									Height: 25
									Text: label-bg-filter
									Align: Right
									Font: TinyBold
						TextField@SEARCH_TEXTFIELD:
							X: 60
							Width: PARENT_WIDTH - 60
							Height: 25
						DropDownButton@CATEGORIES_DROPDOWN:
							X: 60
							Y: 24
							Width: PARENT_WIDTH - 60
							Height: 25
							Font: Bold
						ScrollPanel@TILETEMPLATE_LIST:
							Y: 48
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT - 48
							TopBottomSpacing: 4
							ItemSpacing: 4
							Children:
								ScrollItem@TILEPREVIEW_TEMPLATE:
									Visible: false
									Width: PARENT_WIDTH - 35
									TooltipContainer: TOOLTIP_CONTAINER
									Children:
										TerrainTemplatePreview@TILE_PREVIEW:
											X: 4
											Y: 4
		Container@LAYER_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Visible: false
			Logic: LayerSelectorLogic
			Children:
				ScrollPanel@LAYERTEMPLATE_LIST:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@LAYERPREVIEW_TEMPLATE:
							Visible: false
							IgnoreChildMouseOver: true
							TooltipContainer: TOOLTIP_CONTAINER
							Children:
								ResourcePreview@LAYER_PREVIEW:
									X: 4
									Y: 4
									Visible: false
		Container@ACTOR_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Visible: false
			Logic: ActorSelectorLogic
			Children:
				Background@ACTORS_BG:
					Width: PARENT_WIDTH
					Height: 83
					Background: panel-black
					Children:
						Label@SEARCH_LABEL:
							Y: 1
							Width: 55
							Height: 25
							Text: label-actors-bg-search
							Align: Right
							Font: TinyBold
						TextField@SEARCH_TEXTFIELD:
							X: 60
							Width: PARENT_WIDTH - 60
							Height: 25
						Label@CATEGORIES_LABEL:
							Y: 25
							Width: 55
							Height: 25
							Text: label-bg-filter
							Align: Right
							Font: TinyBold
						DropDownButton@CATEGORIES_DROPDOWN:
							X: 60
							Y: 24
							Width: PARENT_WIDTH - 60
							Height: 25
							Font: Bold
						Label@OWNERS_LABEL:
							Y: 49
							Width: 55
							Height: 25
							Text: label-actor-bg-owner
							Align: Right
							Font: TinyBold
						DropDownButton@OWNERS_DROPDOWN:
							X: 60
							Y: 48
							Width: PARENT_WIDTH - 60
							Height: 25
							Font: Bold
				ScrollPanel@ACTORTEMPLATE_LIST:
					Y: 72
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT - 72
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@ACTORPREVIEW_TEMPLATE:
							Visible: false
							Width: PARENT_WIDTH - 35
							TooltipContainer: TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
							IgnoreChildMouseOver: true
							Children:
								ActorPreview@ACTOR_PREVIEW:
									X: 4
									Y: 4
									Visible: true
		Container@TOOLS_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Visible: false
			Logic: MapToolsLogic
			Children:
				Background@TOOLS_EDIT_PANEL:
					Width: PARENT_WIDTH
					Height: 25
					Background: panel-black
					Children:
						Label@TOOLS_LABEL:
							Y: 1
							Width: 55
							Height: 25
							Text: label-tool-bg-category
							Align: Right
							Font: TinyBold
						DropDownButton@TOOLS_DROPDOWN:
							X: 60
							Width: PARENT_WIDTH - 60
							Height: 25
							Font: Bold
				Background@MARKER_TOOL_PANEL:
					Y: 25
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT - 25
					Background: scrollpanel-bg
					Logic: MapMarkerTilesLogic
					Children:
						ScrollPanel@TILE_COLOR_PANEL:
							X: 6
							Y: 6
							Width: PARENT_WIDTH - 19
							Height: 31
							TopBottomSpacing: 1
							ItemSpacing: 1
							ScrollBar: Hidden
							ScrollbarWidth: 0
							ContentHeight: 31
							Children:
								ScrollItem@TILE_COLOR_TEMPLATE:
									Visible: false
									Height: 29
									Width: 29
									IgnoreChildMouseOver: true
									Children:
										ColorBlock@TILE_PREVIEW:
											X: 2
											Y: 2
											Width: 26
											Height: 26
								ScrollItem@TILE_ICON_TEMPLATE:
									Visible: false
									Height: 29
									Width: 29
									IgnoreChildMouseOver: true
									Children:
										Image@TILE_ERASE:
											X: 6
											Y: 6
											Width: 26
											Height: 26
											ImageCollection: editor
											ImageName: erase
						Button@CLEAR_CURRENT_BUTTON:
							X: 6
							Y: 42
							Width: 100
							Height: 25
							Text: button-marker-tiles-clear-current
							Font: Bold
						Button@CLEAR_ALL_BUTTON:
							X: 111
							Y: 42
							Width: 75
							Height: 25
							Text: button-marker-tiles-clear-all
							Font: Bold
						Label@ALPHA_LABEL:
							X: 6
							Y: 72
							Width: 265
							Height: 25
							Align: Left
							Text: label-marker-alpha
						Slider@ALPHA_SLIDER:
							X: 130
							Y: 72
							Width: 128
							Height: 25
						Label@ALPHA_VALUE:
							X: 260
							Y: 72
							Width: 30
							Height: 25
							Align: Left
						Label@MODE_LABEL:
							X: 6
							Y: 102
							Width: 260
							Height: 25
							Align: Left
							Text: label-marker-mirror-mode
						DropDownButton@MODE_DROPDOWN:
							X: 129
							Y: 102
							Width: 157
							Height: 25
						Label@NUM_SIDES_LABEL:
							X: 6
							Y: 132
							Width: 256
							Height: 25
							Align: Left
							Text: label-marker-layer-num-sides
						Slider@ROTATE_NUM_SIDES_SLIDER:
							X: 130
							Y: 132
							Width: 128
							Height: 25
							Visible: false
						Label@ROTATE_NUM_SIDES_VALUE:
							X: 260
							Y: 132
							Width: 30
							Height: 25
							Align: Left
							Visible: false
						DropDownButton@FLIP_NUM_SIDES_DROPDOWN:
							X: 129
							Y: 132
							Width: 157
							Height: 25
						Label@AXIS_ANGLE_LABEL:
							X: 6
							Y: 162
							Width: 256
							Height: 25
							Align: Left
							Text: label-marker-axis-angle
							Visible: false
						Slider@AXIS_ANGLE_SLIDER:
							X: 130
							Y: 162
							Width: 128
							Height: 25
							Visible: false
						Label@AXIS_ANGLE_VALUE:
							X: 260
							Y: 162
							Width: 30
							Height: 25
							Align: Left
							Visible: false
		Container@HISTORY_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Logic: HistoryLogLogic
			Visible: false
			Children:
				ScrollPanel@HISTORY_LIST:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					CollapseHiddenChildren: True
					TopBottomSpacing: 4
					ItemSpacing: 4
					Children:
						ScrollItem@HISTORY_TEMPLATE:
							X: 4
							Visible: false
							Width: PARENT_WIDTH - 31
							Height: 25
							IgnoreChildMouseOver: true
							TextColor: ffffff
							TextColorDisabled: 8f8f8f
							Children:
								Label@TITLE:
									X: 5
									Width: PARENT_WIDTH
									Height: 25
									Align: Left
		Container@SELECT_WIDGETS:
			X: WINDOW_WIDTH - 295
			Y: 318
			Width: 290
			Height: WINDOW_HEIGHT - 410
			Visible: false
			Children:
				Background@AREA_EDIT_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Background: scrollpanel-bg
					Children:
						Label@AREA_EDIT_TITLE:
							Y: 16
							Width: PARENT_WIDTH
							Height: 24
							Align: Center
							Font: Bold
							Text: label-area-selection
						Label@AREA_FILTERS_LABEL:
							X: 7
							Y: 45
							Width: 150
							Height: 25
							Font: Bold
							Align: Left
							Text: label-selection-filters
						Checkbox@COPY_FILTER_TERRAIN_CHECKBOX:
							X: 7
							Y: 70
							Width: PARENT_WIDTH - 29
							Height: 20
							Text: label-filter-terrain
						Checkbox@COPY_FILTER_RESOURCES_CHECKBOX:
							X: 7
							Y: 95
							Width: PARENT_WIDTH - 29
							Height: 20
							Text: label-filter-resources
						Checkbox@COPY_FILTER_ACTORS_CHECKBOX:
							X: 7
							Y: 120
							Width: PARENT_WIDTH - 29
							Height: 20
							Text: label-filter-actors
						Label@AREA_INFO_TITLE:
							Y: 139
							Width: PARENT_WIDTH
							Height: 24
							Align: Center
							Font: Bold
							Text: label-area-info
						Label@DIAGONAL_LABEL:
							X: 7
							Y: 171
							Width: 120
							Height: 20
							Font: Bold
							Align: Left
							Text: label-selected-area-diagonal
						Label@RESOURCE_LABEL:
							X: 7
							Y: 193
							Width: 120
							Height: 25
							Font: Bold
							Align: Left
							Text: label-selected-area-resources
						Label@DIAGONAL_COUNTER_LABEL:
							X: 140
							Y: 170
							Width: 55
							Height: 22
							Align: Left
						Label@RESOURCES_COUNTER_LABEL:
							X: 140
							Y: 195
							Width: 55
							Height: 22
							Align: Left
						Button@SELECTION_DELETE_BUTTON:
							X: 7
							Y: 222
							Width: 75
							Height: 25
							Text: button-delete-area.label
							Font: Bold
							Key: EditorDeleteSelection
							TooltipTemplate: BUTTON_TOOLTIP
							TooltipText: button-delete-area.tooltip
							TooltipContainer: TOOLTIP_CONTAINER
						Button@SELECTION_CANCEL_BUTTON:
							X: 208
							Y: 222
							Width: 75
							Height: 25
							Text: button-cancel
							Font: Bold
				Background@ACTOR_EDIT_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Background: scrollpanel-bg
					Children:
						Label@ACTOR_TYPE_LABEL:
							Y: 6
							Width: PARENT_WIDTH
							Height: 24
							Align: Center
							Font: Bold
						Label@ACTOR_ID_LABEL:
							Y: 35
							Width: 55
							Height: 25
							Text: label-actor-edit-panel-id
							Align: Right
						TextField@ACTOR_ID:
							X: 69
							Y: 35
							Width: 213
							Height: 25
						Label@ACTOR_ID_ERROR_LABEL:
							X: 69
							Y: 35
							Width: 213
							Height: 25
							Font: TinyBold
							TextColor: FF0000
						Container@ACTOR_INIT_CONTAINER:
							Y: 63
							Width: PARENT_WIDTH
							Children:
								Container@CHECKBOX_OPTION_TEMPLATE:
									Width: PARENT_WIDTH
									Height: 22
									Children:
										Checkbox@OPTION:
											X: 60
											Y: 1
											Width: PARENT_WIDTH - 100
											Height: 20
								Container@SLIDER_OPTION_TEMPLATE:
									Width: PARENT_WIDTH
									Height: 22
									Children:
										Label@LABEL:
											Y: 1
											Width: 55
											Height: 16
											Align: Right
										Slider@OPTION:
											X: 60
											Y: 1
											Width: 170
											Height: 20
										TextField@VALUE:
											X: 232
											Y: 1
											Width: 50
											Height: 20
											Type: Integer
								Container@DROPDOWN_OPTION_TEMPLATE:
									Width: PARENT_WIDTH
									Height: 27
									Children:
										Label@LABEL:
											Y: 2
											Width: 55
											Height: 24
											Align: Right
										DropDownButton@OPTION:
											X: 69
											Y: 1
											Width: 213
											Height: 25
											Font: Bold
						Container@BUTTON_CONTAINER:
							Y: 70
							Children:
								Button@DELETE_BUTTON:
									X: 7
									Width: 75
									Height: 25
									Text: button-delete-actor.label
									Font: Bold
									Key: EditorDeleteSelection
									TooltipTemplate: BUTTON_TOOLTIP
									TooltipText: button-delete-actor.tooltip
									TooltipContainer: TOOLTIP_CONTAINER
								Button@CANCEL_BUTTON:
									X: 127
									Width: 75
									Height: 25
									Text: button-cancel
									Font: Bold
								Button@OK_BUTTON:
									X: 208
									Width: 75
									Height: 25
									Text: button-container-ok
									Font: Bold
		Container@MAP_EDITOR_TAB_CONTAINER:
			Logic: MapEditorTabsLogic
			X: WINDOW_WIDTH - 295
			Y: 294
			Width: 290
			Height: 25
			Children:
				Button@SELECT_TAB:
					Width: 49
					Height: 25
					Key: EditorSelectTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-select-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 20
							Y: 5
							ImageCollection: editor
							ImageName: select
				Button@TILES_TAB:
					X: 49
					Width: 49
					Height: 25
					Key: EditorTilesTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-tiles-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 17
							Y: 5
							ImageCollection: editor
							ImageName: tiles
				Button@OVERLAYS_TAB:
					X: 98
					Width: 48
					Height: 25
					Key: EditorOverlaysTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-overlays-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 17
							Y: 5
							ImageCollection: editor
							ImageName: overlays
				Button@ACTORS_TAB:
					X: 146
					Width: 48
					Height: 25
					Key: EditorActorsTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-actors-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 16
							Y: 5
							ImageCollection: editor
							ImageName: actors
				Button@TOOLS_TAB:
					X: 194
					Width: 48
					Height: 25
					Key: EditorToolsTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-tools-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 16
							Y: 5
							ImageCollection: editor
							ImageName: tools
				Button@HISTORY_TAB:
					X: 241
					Width: 48
					Height: 25
					Key: EditorHistoryTab
					TooltipTemplate: BUTTON_TOOLTIP
					TooltipText: button-map-editor-tab-container-history-tooltip
					TooltipContainer: TOOLTIP_CONTAINER
					Children:
						Image@ICON:
							X: 16
							Y: 5
							ImageCollection: editor
							ImageName: history
		Button@UNDO_BUTTON:
			X: WINDOW_WIDTH - 764
			Y: 5
			Height: 25
			Width: 100
			Text: button-editor-world-root-undo.label
			Font: Bold
			Key: EditorUndo
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-undo.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@REDO_BUTTON:
			X: WINDOW_WIDTH - 654
			Y: 5
			Height: 25
			Width: 100
			Text: button-editor-world-root-redo.label
			Font: Bold
			Key: EditorRedo
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-redo.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@COPY_BUTTON:
			X: WINDOW_WIDTH - 544
			Y: 5
			Height: 25
			Width: 100
			Text: button-editor-world-root-copy.label
			Font: Bold
			Key: EditorCopy
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-copy.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		Button@PASTE_BUTTON:
			X: WINDOW_WIDTH - 434
			Y: 5
			Height: 25
			Width: 100
			Text: button-editor-world-root-paste.label
			Font: Bold
			Key: EditorPaste
			TooltipTemplate: BUTTON_TOOLTIP
			TooltipText: button-editor-world-root-paste.tooltip
			TooltipContainer: TOOLTIP_CONTAINER
		DropDownButton@OVERLAY_BUTTON:
			X: WINDOW_WIDTH - 914
			Y: 5
			Width: 140
			Height: 25
			Text: dropdownbutton-editor-world-root-overlay-button
			Font: Bold
		Label@COORDINATE_LABEL:
			X: 10
			Width: 50
			Height: 25
			Align: Left
			Font: Bold
			Contrast: true
		Label@CASH_LABEL:
			X: 125
			Width: 50
			Height: 25
			Align: Left
			Font: Bold
			Contrast: true

ScrollPanel@CATEGORY_FILTER_PANEL:
	Width: 230
	TopBottomSpacing: 5
	ItemSpacing: 5
	Children:
		Container@SELECT_CATEGORIES_BUTTONS:
			Width: PARENT_WIDTH
			Height: 25
			Children:
				Button@SELECT_ALL:
					X: 10
					Y: 0 - 5
					Width: 88
					Height: 25
					Text: button-select-categories-buttons-all
					Font: Bold
				Button@SELECT_NONE:
					X: 10 + 88 + 10
					Y: 0 - 5
					Width: 88
					Height: 25
					Text: button-select-categories-buttons-none
					Font: Bold
		Checkbox@CATEGORY_TEMPLATE:
			X: 5
			Width: PARENT_WIDTH - 29
			Height: 20
			Visible: false

ScrollPanel@OVERLAY_PANEL:
	Width: 140
	Height: 80
	ItemSpacing: 5
	TopBottomSpacing: 0
	Children:
		Checkbox@CATEGORY_TEMPLATE:
			X: 5
			Y: 5
			Width: PARENT_WIDTH - 29
			Height: 20
			Visible: false
