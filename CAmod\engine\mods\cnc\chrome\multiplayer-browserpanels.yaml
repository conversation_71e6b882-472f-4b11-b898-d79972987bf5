ScrollPanel@MULTIPLAYER_CLIENT_LIST:
	Width: PARENT_WIDTH
	Height: 225
	IgnoreChildMouseOver: true
	Children:
		ScrollItem@HEADER:
			Background: scrollheader
			Width: PARENT_WIDTH - 27
			Height: 13
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					Font: TinyBold
					Width: PARENT_WIDTH
					Height: 13
					Align: Center
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Image@FLAG:
					X: 4
					Y: 5
					Width: 32
					Height: 16
					Visible: False
				Label@LABEL:
					X: 40
					Width: PARENT_WIDTH - 50
					Height: 25
					Shadow: True
				Label@NOFLAG_LABEL:
					X: 5
					Width: PARENT_WIDTH
					Height: 25
					Shadow: True

ScrollPanel@MULTIPLAYER_FILTER_PANEL:
	Width: PARENT_WIDTH
	Height: 130
	Background: panel-black
	Children:
		Checkbox@WAITING_FOR_PLAYERS:
			X: 5
			Y: 5
			Width: PARENT_WIDTH - 29
			Height: 20
			Text: checkbox-multiplayer-filter-panel-waiting-for-players
			TextColor: 32CD32
			Font: Regular
		Checkbox@EMPTY:
			X: 5
			Y: 30
			Width: PARENT_WIDTH - 29
			Height: 20
			Text: checkbox-multiplayer-filter-panel-empty
			Font: Regular
		Checkbox@PASSWORD_PROTECTED:
			X: 5
			Y: 55
			Width: PARENT_WIDTH - 29
			Height: 20
			Text: checkbox-multiplayer-filter-panel-password-protected
			TextColor: FF0000
			Font: Regular
		Checkbox@ALREADY_STARTED:
			X: 5
			Y: 80
			Width: PARENT_WIDTH - 29
			Height: 20
			Text: checkbox-multiplayer-filter-panel-already-started
			TextColor: FFA500
			Font: Regular
		Checkbox@INCOMPATIBLE_VERSION:
			X: 5
			Y: 105
			Width: PARENT_WIDTH - 29
			Height: 20
			Text: checkbox-multiplayer-filter-panel-incompatible-version
			TextColor: BEBEBE
			Font: Regular
