fact:
	Defaults:
		Filename: fact.shp
	build:
		Start: 4
		Length: 20
		Tick: 100
	idle:
		Length: 4
		Tick: 100
	damaged-idle:
		Start: 24
		Length: 4
		Tick: 100
	damaged-build:
		Start: 28
		Length: 20
		Tick: 100
	dead:
		Start: 48
		Tick: 800
	make:
		Filename: factmake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib2.des
			WINTER: bib2.win
			SNOW: bib2.sno
			TEMPERAT: bib2.tem
			JUNGLE: bib2.jun
		Length: *
	icon:
		Filename: facticnh.shp

nuke:
	Defaults:
		Filename: nuke.shp
	idle:
		Length: 4
		Tick: 1000
	damaged-idle:
		Start: 4
		Length: 4
		Tick: 1000
	dead:
		Start: 8
		Tick: 800
	make:
		Filename: nukemake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
	icon:
		Filename: nukeicnh.tem

proc:
	Defaults:
		Filename: proc.shp
	idle:
		Length: 6
		Tick: 120
		Offset: 2,4
	damaged-idle:
		Start: 30
		Length: 6
		Tick: 120
		Offset: 2,4
	dead:
		Start: 60
		Tick: 800
		Offset: 2,4
	make:
		Filename: procmake.shp
		Length: *
		Tick: 80
		Offset: 2,4
	resources:
		Filename: proctwr.shp
		Length: 6
		Offset: -30,-17
	damaged-resources:
		Filename: proctwr.shp
		Start: 6
		Length: 6
		Offset: -30,-17
	bib:
		TilesetFilenames:
			DESERT: bib2.des
			WINTER: bib2.win
			SNOW: bib2.sno
			TEMPERAT: bib2.tem
			JUNGLE: bib2.jun
		Length: *
	icon:
		Filename: procicnh.tem

silo:
	Defaults:
		Filename: silo.shp
	idle:
		Offset: 0,-1
	damaged-idle:
		Start: 5
		Offset: 0,-1
	dead:
		Start: 10
		Offset: 0,-1
		Tick: 800
	stages:
		Length: 5
		Offset: 0,-1
	damaged-stages:
		Start: 5
		Length: 5
		Offset: 0,-1
	make:
		Filename: silomake.shp
		Length: *
		Tick: 80
		Offset: 0,-1
	bib:
		Filename: mbSILO.tem
		TilesetFilenames:
			DESERT: mbSILO.des
		Length: *
		Offset: 0,1
	icon:
		Filename: siloicnh.tem

hand:
	Defaults:
		Filename: hand.shp
		Offset: 0,-8
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 2
		Tick: 800
	make:
		Filename: handmake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
		Offset: 0,0
	icon:
		Filename: handicnh.tem
		Offset: 0,0

pyle:
	Defaults:
		Filename: pyle.shp
	idle:
		Length: 10
		Tick: 100
		ZOffset: -511
	damaged-idle:
		Start: 10
		Length: 10
		Tick: 100
		ZOffset: -511
	dead:
		Start: 20
		Tick: 800
	make:
		Filename: pylemake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
	icon:
		Filename: pyleicnh.tem

weap:
	Defaults:
		Filename: weap.shp
		Offset: 0,-12
	idle:
		ZOffset: -511
	damaged-idle:
		Start: 1
		ZOffset: -511
	dead:
		Start: 2
		Tick: 800
	build-top:
		Filename: weap2.shp
		Length: 10
		ZOffset: -1024
	damaged-build-top:
		Filename: weap2.shp
		Start: 10
		Length: 10
		ZOffset: -1024
	place:
		Filename: weapmake.shp
		Start: 19
	make:
		Filename: weapmake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib2.des
			WINTER: bib2.win
			SNOW: bib2.sno
			TEMPERAT: bib2.tem
			JUNGLE: bib2.jun
		Length: *
		Offset: 0,0
	icon:
		Filename: weapicnh.tem
		Offset: 0,0

afld:
	Defaults:
		Filename: afld.shp
	idle:
		Tick: 120
		ZOffset: -1023
	damaged-idle:
		Start: 16
		Tick: 120
		ZOffset: -1023
	active:
		Length: 16
		Tick: 120
		ZOffset: -1023
	damaged-active:
		Start: 16
		Length: 16
		Tick: 120
		ZOffset: -1023
	idle-dish:
		Filename: afld_d.shp
		Length: 16
		Tick: 160
	damaged-idle-dish:
		Filename: afld_d.shp
		Start: 16
		Length: 16
		Tick: 160
	dead:
		Start: 32
		ZOffset: -1023
		Tick: 800
	make:
		Filename: afldmake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib1.des
			WINTER: bib1.win
			SNOW: bib1.sno
			TEMPERAT: bib1.tem
			JUNGLE: bib1.jun
		Length: *
	icon:
		Filename: afldicnh.tem

hq:
	Defaults:
		Filename: hq.shp
	idle:
		Length: 16
		Tick: 100
	damaged-idle:
		Start: 16
		Length: 16
		Tick: 100
	dead:
		Start: 32
		Tick: 800
	make:
		Filename: hqmake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
	icon:
		Filename: hqicnh.tem

nuk2:
	Defaults:
		Filename: nuk2.shp
	idle:
		Length: 4
		Tick: 1000
	damaged-idle:
		Start: 4
		Length: 4
		Tick: 1000
	dead:
		Start: 8
		Tick: 800
	make:
		Filename: nuk2make.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
	icon:
		Filename: nuk2icnh.tem

hpad:
	Defaults:
		Filename: hpad.shp
	idle:
		ZOffset: -1023
	damaged-idle:
		Start: 7
		ZOffset: -1023
	active:
		Start: 1
		Length: 6
		Tick: 100
		ZOffset: -1023
	damaged-active:
		Start: 8
		Length: 6
		Tick: 100
		ZOffset: -1023
	dead:
		Start: 14
		ZOffset: -1023
		Tick: 800
	make:
		Filename: hpadmake.shp
		Length: *
		Tick: 80
	icon:
		Filename: hpadicnh.tem

fix:
	Defaults:
		Filename: fix.shp
	idle:
		ZOffset: -1c511
	damaged-idle:
		Start: 7
		ZOffset: -1c511
	active:
		Length: 7
		ZOffset: -1c511
	damaged-active:
		Start: 7
		Length: 7
		ZOffset: -1c511
	dead:
		Start: 14
		ZOffset: -1c511
		Tick: 800
	make:
		Filename: fixmake.shp
		Length: 14
		Tick: 60
	bib:
		Filename: mbFIX.tem
		TilesetFilenames:
			DESERT: mbFIX.des
		Length: *
		Offset: 0,-9
	icon:
		Filename: fixicnh.tem

eye:
	Defaults:
		Filename: eye.shp
	idle:
		Length: 16
		Tick: 100
	damaged-idle:
		Start: 16
		Length: 16
		Tick: 100
	dead:
		Start: 32
		Tick: 800
	make:
		Filename: eyemake.shp
		Length: *
		Tick: 80
	bib:
		TilesetFilenames:
			DESERT: bib3.des
			WINTER: bib3.win
			SNOW: bib3.sno
			TEMPERAT: bib3.tem
			JUNGLE: bib3.jun
		Length: *
	icon:
		Filename: eyeicnh.tem

tmpl:
	Defaults:
		Filename: tmpl.shp
		Offset: 0,-12
	idle:
	damaged-idle:
		Start: 5
	active:
		Length: 5
		Tick: 200
	smoke:
		Filename: atomdoor.shp
		Length: *
		Offset: -1,-47
	damaged-active:
		Start: 5
		Length: 5
	dead:
		Start: 10
		Tick: 800
	make:
		Filename: tmplmake.shp
		Length: *
		Tick: 60
	bib:
		TilesetFilenames:
			DESERT: bib2.des
			WINTER: bib2.win
			SNOW: bib2.sno
			TEMPERAT: bib2.tem
			JUNGLE: bib2.jun
		Length: *
		Offset: 0,0
	icon:
		Filename: tmplicnh.tem
		Offset: 0,0

obli:
	Defaults:
		Filename: obli.shp
		Offset: 0,-12
	idle:
	damaged-idle:
		Start: 4
	active:
		Length: 4
		Tick: 680
	damaged-active:
		Start: 4
		Length: 4
		Tick: 680
	dead:
		Start: 8
		Tick: 800
	make:
		Filename: oblimake.shp
		Length: 13
		Tick: 80
	bib:
		Filename: mbOBLI.tem
		TilesetFilenames:
			DESERT: mbOBLI.des
		Length: *
		Offset: -1,-3
	icon:
		Filename: obliicnh.tem
		Offset: 0,0

brik:
	Defaults:
		Filename: brik.shp
	idle:
		Length: 16
	scratched-idle:
		Start: 16
		Length: 16
	damaged-idle:
		Start: 32
		Length: 16
	icon:
		Filename: brikicnh.tem

sbag:
	idle:
		Filename: sbag.shp
		Length: 16
	icon:
		Filename: sbagicnh.tem

cycl:
	Defaults:
		Filename: cycl.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16
	icon:
		Filename: cyclicnh.tem

barb:
	Defaults:
		Filename: barb.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16

wood:
	Defaults:
		Filename: wood.shp
	idle:
		Length: 16
	damaged-idle:
		Start: 16
		Length: 16

gun:
	Defaults:
		Filename: gun.shp
	idle: # Empty first frame. We need WithSpriteBody for the make anim, and WSB needs at least a placeholder default sequence to work
		Filename: gunmake.shp
	turret:
		Facings: 32
		UseClassicFacings: True
	recoil:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	damaged-turret:
		Start: 64
		Facings: 32
		UseClassicFacings: True
	damaged-recoil:
		Start: 96
		Facings: 32
		UseClassicFacings: True
	make:
		Filename: gunmake.shp
		Length: *
		Tick: 80
	muzzle:
		Filename: gunfire2.shp
		Length: *
	bib:
		Filename: mbGUN.tem
		TilesetFilenames:
			DESERT: mbGUN.des
		Length: *
		Offset: -1,-1
	icon:
		Filename: gunicnh.tem

sam:
	Defaults:
		Filename: sam.shp
	closed-idle:
		Start: 0
	opening:
		Start: 1
		Length: 16
		Tick: 30
	idle:
		Start: 17
		Facings: 32
		UseClassicFacings: True
	closing:
		Start: 50
		Length: 14
		Tick: 30
	damaged-closed-idle:
		Start: 64
	damaged-opening:
		Start: 65
		Length: 16
		Tick: 30
	damaged-idle:
		Start: 81
		Facings: 32
		UseClassicFacings: True
	damaged-closing:
		Start: 114
		Length: 14
		Tick: 30
	dead:
		Start: 128
		Tick: 800
	place:
		Start: 0
	make:
		Filename: sammake.shp
		Length: 20
		Tick: 50
	muzzle:
		Filename: samfire.shp
		Length: 18
		Facings: 8
	icon:
		Filename: samicnh.tem

gtwr:
	Defaults:
		Filename: gtwr.shp
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 2
		Tick: 800
	make:
		Filename: gtwrmake.shp
		Frames: 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 19
		Length: 18
		Tick: 80
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	bib:
		Filename: mbGTWR.tem
		TilesetFilenames:
			DESERT: mbGTWR.des
		Length: *
		Offset: 0,-2
	icon:
		Filename: gtwricnh.tem

atwr:
	Defaults:
		Filename: atwr.shp
		Offset: 0,-13
	idle:
	damaged-idle:
		Start: 1
	dead:
		Start: 2
		Tick: 800
	make:
		Filename: atwrmake.shp
		Length: *
		Tick: 80
	muzzle:
		Filename: gunfire2.shp
		Length: *
	bib:
		Filename: mbGTWR.tem
		TilesetFilenames:
			DESERT: mbGTWR.des
		Length: *
		Offset: -3,0
	icon:
		Filename: atwricnh.tem
		Offset: 0,0

hosp:
	Defaults:
		Filename: hosp.shp
	idle:
		Length: 4
		Tick: 100
		Offset: 0,-2
	damaged-idle:
		Start: 4
		Length: 4
		Offset: 0,-2
	make:
		Filename: hospmake.shp
		Length: *
		Tick: 80
		Offset: 0,-2
	bib:
		Filename: mbHOSP.tem
		TilesetFilenames:
			DESERT: mbHOSP.des
		Length: *
		Offset: 0,1

hosp.husk:
	idle:
		Filename: hosp.shp
		Start: 8
	bib:
		Filename: mbHOSP.tem
		TilesetFilenames:
			DESERT: mbHOSP.des
		Length: *
		Offset: 0,1

bio:
	Defaults:
		Filename: bio.shp
	idle:
	damaged-idle:
		Start: 1
	make:
		Filename: biomake.shp
		Length: *
		Tick: 80

bio.husk:
	idle:
		Filename: bio.shp
		Start: 2

miss:
	Defaults:
		Filename: miss.shp
	idle:
		Offset: 0,-1
	damaged-idle:
		Start: 1
		Offset: 0,-1
	make:
		Filename: missmake.shp
		Length: *
		Tick: 80
		Offset: 0,-1
	bib:
		Filename: mbMISS.tem
		TilesetFilenames:
			DESERT: mbMISS.des
		Length: *
		Offset: 0,1
	icon:
		Filename: missicnh.shp

miss.husk:
	idle:
		Filename: miss.shp
		Start: 2
	bib:
		Filename: mbMISS.tem
		TilesetFilenames:
			DESERT: mbMISS.des
		Length: *
		Offset: 0,1
