--[[
   Copyright (c) The OpenRA Developers and Contributors
   This file is part of OpenRA, which is free software. It is made
   available to you under the terms of the GNU General Public License
   as published by the Free Software Foundation, either version 3 of
   the License, or (at your option) any later version. For more
   information, see COPYING.
]]

HarkonnenBase = { <PERSON><PERSON>ard, HPower1, HPower2, HBarracks, HOutpost }
HarkonnenBaseAreaTrigger = { CPos.<PERSON>(31, 37), CPos<PERSON>(32, 37), <PERSON><PERSON><PERSON>(33, 37), <PERSON><PERSON><PERSON>(34, 37), <PERSON><PERSON><PERSON>(35, 37), <PERSON><PERSON><PERSON>(36, 37), <PERSON><PERSON><PERSON>(37, 37), <PERSON><PERSON><PERSON>(38, 37), <PERSON><PERSON><PERSON>(39, 37), <PERSON><PERSON><PERSON>(40, 37), <PERSON><PERSON><PERSON>(41, 37), <PERSON><PERSON><PERSON>(42, 37), <PERSON><PERSON><PERSON>(42, 38), <PERSON><PERSON><PERSON>(42, 39), <PERSON><PERSON><PERSON>(42, 40), <PERSON><PERSON><PERSON>(42, 41), <PERSON><PERSON><PERSON>(42, 42), <PERSON><PERSON><PERSON>(42, 43), <PERSON><PERSON><PERSON>(42, 44), <PERSON><PERSON><PERSON>(42, 45), <PERSON><PERSON><PERSON>(42, 46), <PERSON><PERSON><PERSON>(42, 47), <PERSON><PERSON><PERSON>(42, 48), <PERSON><PERSON><PERSON>(42, 49) }

HarkonnenReinforcements =
{
	easy =
	{
		{ "light_inf", "trike" },
		{ "light_inf", "trike" },
		{ "light_inf", "light_inf", "light_inf", "trike", "trike" }
	},

	normal =
	{
		{ "light_inf", "trike" },
		{ "light_inf", "trike" },
		{ "light_inf", "light_inf", "light_inf", "trike", "trike" },
		{ "light_inf", "light_inf" },
		{ "light_inf", "light_inf", "light_inf" },
		{ "light_inf", "trike" }
	},

	hard =
	{
		{ "trike", "trike" },
		{ "light_inf", "trike" },
		{ "light_inf", "trike" },
		{ "light_inf", "light_inf", "light_inf", "trike", "trike" },
		{ "light_inf", "light_inf" },
		{ "trike", "trike" },
		{ "light_inf", "light_inf", "light_inf" },
		{ "light_inf", "trike" },
		{ "trike", "trike" }
	}
}

HarkonnenAttackPaths =
{
	{ HarkonnenEntry3.Location, HarkonnenRally3.Location },
	{ HarkonnenEntry4.Location, HarkonnenRally5.Location },
	{ HarkonnenEntry4.Location, HarkonnenRally6.Location },
	{ HarkonnenEntry5.Location, HarkonnenRally4.Location }
}

InitialHarkonnenReinforcementsPaths =
{
	{ HarkonnenEntry1.Location, HarkonnenRally1.Location },
	{ HarkonnenEntry2.Location, HarkonnenRally2.Location }
}

InitialHarkonnenReinforcements =
{
	{ "trike", "trike" },
	{ "light_inf", "light_inf" }
}

HarkonnenAttackDelay =
{
	easy = DateTime.Minutes(5),
	normal = DateTime.Minutes(2) + DateTime.Seconds(40),
	hard = DateTime.Minutes(1) + DateTime.Seconds(20)
}

HarkonnenAttackWaves =
{
	easy = 3,
	normal = 6,
	hard = 9
}

OrdosReinforcements = { "light_inf", "light_inf", "raider" }
OrdosEntryPath = { OrdosEntry.Location, OrdosRally.Location }

Tick = function()
	if Ordos.HasNoRequiredUnits() then
		Harkonnen.MarkCompletedObjective(KillOrdos)
	end

	if Harkonnen.HasNoRequiredUnits() and not Ordos.IsObjectiveCompleted(KillHarkonnen) then
		Media.DisplayMessage(UserInterface.GetFluentMessage("harkonnen-annihilated"), Mentat)
		Ordos.MarkCompletedObjective(KillHarkonnen)
	end
end

WorldLoaded = function()
	Harkonnen = Player.GetPlayer("Harkonnen")
	Ordos = Player.GetPlayer("Ordos")

	InitObjectives(Ordos)
	KillOrdos = AddPrimaryObjective(Harkonnen, "")
	KillHarkonnen = AddPrimaryObjective(Ordos, "destroy-harkonnen-forces")

	Camera.Position = OConyard.CenterPosition

	Trigger.OnAllKilled(HarkonnenBase, function()
		Utils.Do(Harkonnen.GetGroundAttackers(), IdleHunt)
	end)

	Trigger.AfterDelay(DateTime.Minutes(1), function()
		Media.PlaySpeechNotification(Ordos, "Reinforce")
		Reinforcements.ReinforceWithTransport(Ordos, "carryall.reinforce", OrdosReinforcements, OrdosEntryPath, { OrdosEntryPath[1] })
	end)

	TriggerCarryallReinforcements(Ordos, Harkonnen, HarkonnenBaseAreaTrigger, InitialHarkonnenReinforcements[1], InitialHarkonnenReinforcementsPaths[1])
	TriggerCarryallReinforcements(Ordos, Harkonnen, HarkonnenBaseAreaTrigger, InitialHarkonnenReinforcements[2], InitialHarkonnenReinforcementsPaths[2])

	local path = function() return Utils.Random(HarkonnenAttackPaths) end
	SendCarryallReinforcements(Harkonnen, 0, HarkonnenAttackWaves[Difficulty], HarkonnenAttackDelay[Difficulty], path, HarkonnenReinforcements[Difficulty])
	Trigger.AfterDelay(0, ActivateAI)
end
