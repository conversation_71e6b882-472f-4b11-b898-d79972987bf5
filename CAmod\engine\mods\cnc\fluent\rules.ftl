## player.yaml
options-tech-level =
    .low = Low
    .medium = Medium
    .no-powers = No Superpowers
    .unrestricted = Unrestricted

checkbox-redeployable-mcvs =
    .label = Redeployable MCVs
    .description = Allows Construction Yards to undeploy

checkbox-stealth-deliveries =
    .label = Stealth Deliveries
    .description = Nod's delivery plane is cloaked

notification-new-construction-options = New construction options.
notification-cannot-deploy-here = Cannot deploy here.
notification-low-power = Low power.
notification-base-under-attack = Base under attack.
notification-ally-under-attack = Our ally is under attack.
notification-silos-needed = Silos needed.

## world.yaml
options-starting-units =
    .mcv-only = MCV Only
    .light-support = Light Support
    .heavy-support = Heavy Support

dropdown-map-creeps =
    .label = Creep Actors
    .description = Hostile forces spawn on the battlefield

resource-tiberium = Tiberium

faction-random =
    .name = Any
    .description = Random Faction
     A random faction is chosen at the start of the game

faction-gdi =
    .name = GDI
    .description = Global Defense Initiative
     The GDI is an international military branch of the United Nations tasked
     with keeping world peace. Commanding the combined forces of the world's
     most powerful nations, it possess an unmatched arsenal of high-tech weaponry.

faction-nod =
    .name = Nod
    .description = Brotherhood of Nod
     The Brotherhood is a religious cult centered around their leader <PERSON>
     and the alien substance Tiberium. They use stealth technology
     and guerilla tactics to defeat those who oppose them.

## defaults.yaml
notification-unit-lost = Unit lost.
notification-unit-promoted = Unit promoted.
notification-building-captured = Building captured.
notification-civ-building-captured = Civilian building captured.
notification-structure-lost = Structure lost.

## aircraft.yaml
actor-tran =
    .name = Chinook Transport
    .description =
    Fast Infantry Transport Helicopter.
      Unarmed
    .encyclopedia =
    The chinook is a flying transport capable of carrying a detachement of infantry. It is mostly used to transport commandos or engineers for backline-destruction.

    Like all aircraft, it can be handy in a pinch to crush a commando by landing!

actor-heli =
    .name = Apache Longbow
    .description =
    Helicopter Gunship with chainguns.
      Strong vs Infantry, Light Vehicles and Aircraft
      Weak vs Tanks
    .encyclopedia =
    Has more health than the Orca and acts as a support unit. It works well with artillery, as it can kill approaching light vehicles.

    Ammo is automatically reloaded in the air.

actor-orca =
    .name = Orca
    .description =
    Helicopter gunship with AG missiles.
      Strong vs Buildings and Tanks
      Weak vs Infantry
    .encyclopedia =
    Fast but fragile, the Orca harasses enemy vehicles and aircraft with AT missiles. It is best used opportunistically, and can be kept alive longer with careful micro. Its AA missiles are effective but have a hard time hitting fast-moving air targets.

    Ammo is automatically reloaded in the air.

actor-c17-name = Supply Aircraft
actor-a10-name = A10 Bomber
actor-tran-husk-name = Chinook Transport (Destroyed)
actor-heli-husk-name = Apache Longbow (Destroyed)
actor-orca-husk-name = Orca (Destroyed)

## civilian-desert.yaml
actor-v20-name = Sala's House
actor-v20-husk-name = Sala's House (Destroyed)
actor-v21-name = Abdul's House
actor-v21-husk-name = Abdul's House (Destroyed)
actor-v22-name = Pablo's Wicked Pub
actor-v22-husk-name = Pablo's Wicked Pub (Destroyed)
actor-v23-name = Village Well
actor-v23-husk-name = Village Well (Destroyed)
actor-v24-name = Camel Trader
actor-v24-husk-name = Camel Trader (Destroyed)
actor-v25-name = Church
actor-v25-husk-name = Church (Destroyed)
actor-v26-name = Ali's House
actor-v26-husk-name = Ali's House (Destroyed)
actor-v27-name = Trader Ted's
actor-v27-husk-name = Trader Ted's (Destroyed)
actor-v28-name = Menelik's House
actor-v28-husk-name = Menelik's House (Destroyed)
actor-v29-name = Prestor John's House
actor-v29-husk-name = Prestor John's House (Destroyed)
actor-v30-name = Village Well
actor-v30-husk-name = Village Well (Destroyed)
actor-v31-name = Witch Doctor's Hut
actor-v31-husk-name = Witch Doctor's Hut (Destroyed)
actor-v32-name = Rikitikitembo's Hut
actor-v32-husk-name = Rikitikitembo's Hut (Destroyed)
actor-v33-name = Roarke's Hut
actor-v33-husk-name = Roarke's Hut (Destroyed)
actor-v34-name = Mubasa's Hut
actor-v34-husk-name = Mubasa's Hut (Destroyed)
actor-v35-name = Aksum's Hut
actor-v35-husk-name = Aksum's Hut (Destroyed)
actor-v36-name = Mambo's Hut
actor-v36-husk-name = Mambo's Hut (Destroyed)
actor-v37-name = The Studio
actor-v37-husk-name = The Studio (Destroyed)

## civilian.yaml
actor-v01-name = Church
actor-v01-husk-name = Church (Destroyed)
actor-v02-name = Hans and Gretel's House
actor-v02-husk-name = Hans and Gretel's House (Destroyed)
actor-v03-name = Hewitt's House
actor-v03-husk-name = Hewitt's House (Destroyed)
actor-v04-name = Ricktor's House
actor-v04-husk-name = Ricktor's House (Destroyed)
actor-v05-name = Gretchkin's House
actor-v05-husk-name = Gretchkin's House (Destroyed)
actor-v06-name = The Barn
actor-v06-husk-name = The Barn (Destroyed)
actor-v07-name = Damon's Pub
actor-v07-husk-name = Damon's Pub (Destroyed)
actor-v08-name = Fran's House
actor-v08-husk-name = Fran's House (Destroyed)
actor-v09-name = Music Factory
actor-v09-husk-name = Music Factory (Destroyed)
actor-v10-name = Toymaker's
actor-v10-husk-name = Toymaker's (Destroyed)
actor-v11-name = Ludwig's House
actor-v11-husk-name = Ludwig's House (Destroyed)
actor-v14-name = Wheat Field
actor-v14-husk-name = Wheat Field (Destroyed)
actor-v15-name = Fallow Field
actor-v15-husk-name = Fallow Field (Destroyed)
actor-v16-name = Corn Field
actor-v16-husk-name = Corn Field (Destroyed)
actor-v17-name = Celery Field
actor-v17-husk-name = Celery Field (Destroyed)
actor-v18-name = Potato Field
actor-v18-husk-name = Potato Field (Destroyed)
actor-arco-name = Oil Pump
actor-arco-husk-name = Oil Pump (Destroyed)
actor-barb-name = Barbwire Fence
actor-wood-name = Wooden Fence
actor-delphi-name = Agent Delphi
actor-chan-name = Dr. Chan
actor-moebius-name = Dr. Moebius

## defaults.yaml
meta-vehicle-generic-name = Vehicle
meta-tank-generic-name = Tank
meta-helicopter-generic-name = Helicopter
meta-soldier-generic-name = Soldier
meta-civinfantry-name = Civilian
meta-viceroid-name = Visceroid
meta-ship-generic-name = Ship
meta-building-generic-name = Structure
meta-civbuilding-generic-name = Civilian Building
meta-civbuildinghusk-generic-name = Civilian Building (Destroyed)
meta-civfield-generic-name = Field

meta-civhaystackorigloo =
    .winter-name = Igloo
    .summer-name = Haystack

meta-civhaystackorigloohusk =
    .winter-name = Igloo (Destroyed)
    .summer-name = Haystack (Destroyed)

meta-tree-name = Tree
meta-treehusk-name = Tree (Burnt)
meta-tibtree-name = Blossom Tree
meta-rock-name = Rock
meta-husk-generic-name = Destroyed Vehicle
meta-helicopterhusk-generic-name = Destroyed Helicopter
meta-bridge-name = Bridge

meta-crate =
    .name = Crate
    .generic-name = Crate

## husks.yaml
actor-mcv-husk-name = Mobile Construction Vehicle (Destroyed)
actor-harv-husk-name = Harvester (Destroyed)
actor-apc-husk-name = APC (Destroyed)
actor-ftnk-husk-name = Flame Tank (Destroyed)
actor-arty-husk-name = Artillery (Destroyed)
actor-bggy-husk-name = Nod Buggy (Destroyed)
actor-bike-husk-name = Recon Bike (Destroyed)
actor-jeep-husk-name = Hum-vee (Destroyed)
actor-ltnk-husk-name = Light Tank (Destroyed)
actor-mtnk-husk-name = Medium Tank (Destroyed)
actor-htnk-husk-name = Mammoth Tank (Destroyed)
actor-msam-husk-name = Rocket Launcher (Destroyed)
actor-mlrs-husk-name = Mobile SAM (Destroyed)
actor-stnk-husk-name = Stealth Tank (Destroyed)
actor-truck-husk-name = Supply Truck (Destroyed)

## infantry.yaml
actor-e1 =
    .name = Minigunner
    .description =
    General-purpose infantry.
      Strong vs Infantry
      Weak vs Vehicles
    .encyclopedia =
    The humble Minigunner is a highly specialized infantry unit designed to soak damage and kill other infantry.

    Like most TD units, they can see farther than they can shoot, and work well in packs to eliminate enemy infantry.

actor-e2 =
    .name = Grenadier
    .description =
    Fast infantry with grenades.
      Strong vs Buildings, slow-moving targets
    .encyclopedia =
    Fast and able to keep up with medium tanks, letting it work well with flanking armies.

    While not as powerful as the Minigunner or a Rocket soldier, its speed can be used to your advantage.

actor-e3 =
    .name = Rocket Soldier
    .description =
    Anti-tank/Anti-aircraft infantry.
      Strong vs Tanks and Aircraft
      Weak vs Infantry
    .encyclopedia =
    Highly vulnerable but excelling at eliminating enemy armor and aircraft. It is the slowest unit in the game, lagging behind other infantry and even mammoth tanks.

    Its large weapons range makes up for its slow speed, making it most effective when used defensively, where it can be protected and provide vision.

actor-e4 =
    .name = Flamethrower
    .description =
    Advanced anti-infantry unit.
      Strong vs Infantry and Buildings
      Weak vs Tanks
    .encyclopedia =
    Good for flanking and burning down structures. It has more health than a standard Minigunner, but its short weapons range makes it difficult to use in large armies.

actor-e5 =
    .name = Chemical Warrior
    .description =
    Advanced general-purpose infantry.
      Strong vs all Ground units
    .encyclopedia =
    Similar stats to the Flamethrower but deals good damage vs heavy armor. Great for stopping enemy armor from crushing your forces.

    It can walk on Tiberium without taking damage, making it particularly useful for harvester harassment on certain maps.

actor-e6 =
    .name = Engineer
    .description =
    Damages and captures enemy structures.
    Repairs destroyed vehicles.
      Unarmed
    .encyclopedia =
    Like the rocket soldier, the Engineer is slow and requires escorts to be used effectively. It can instantly capture structures but is consumed in the process.

    Capturing a civilian structure requires only one engineer, while capturing an enemy structure typically requires two engineers, or one if the structure is heavily damaged.

    Engineers can also be used to repair friendly structures or restore husks from destroyed vehicles.

actor-rmbo =
    .name = Commando
    .description =
    Elite sniper infantry unit.
      Strong vs Infantry and Buildings
      Weak vs Vehicles
    .encyclopedia =
    Equipped with a long-range sniper rifle, the Commando fires slowly but can eliminate enemy infantry from a distance when well-supported.

    It carries C4, for surprise backline structure destruction.

actor-pvice =
    .description =
    Mutated abomination that spits liquid Tiberium.
      Strong vs Infantry and Buildings
      Weak vs Aircraft
    .encyclopedia =
    A mutated lifeform created from the strange properties of Tiberium, when infantry units are expose to it.

    They regenerate health quickly and have a short-range Tiberium weapon.

actor-steg =
    .name = Stegosaurus
    .description =
    A large, heavily built,
    herbivorous quadruped.

actor-trex =
    .name = Tyrannosaurus rex
    .description =
    Bipedal carnivore with
    a massive skull.

actor-tric =
    .name = Triceratops
    .description =
    Quadruped with large bony
    frill and three horns.

actor-rapt =
    .name = Velociraptor
    .description =
    Bipedal with enlarged sickle-shaped
    claw on each hindfoot.

## misc.yaml
actor-wcrate-name = Wooden Crate
actor-scrate-name = Steel Crate
actor-mpspawn-name = (multiplayer starting point)
actor-waypoint-name = (waypoint for scripted behavior)
actor-camera-name = (reveals area to owner)
actor-camera-small-name = (reveals small area to owner)
actor-flare-name = Flare

## ships.yaml
actor-boat-name = Gunboat
actor-lst-name = Landing Craft

## structures.yaml
notification-construction-complete = Construction complete.
notification-unit-ready = Unit ready.
notification-reinforcements-have-arrived = Reinforcements have arrived.
notification-unable-to-build-more = Unable to build more.
notification-unable-to-comply-building-in-progress = Unable to comply. Building in progress.
notification-repairing = Repairing.
notification-ion-cannon-charging = Ion cannon charging.
notification-ion-cannon-ready = Ion cannon ready.
notification-select-target = Select target.
notification-insufficient-power = Insufficient power.
notification-airstrike-ready = Airstrike ready.
notification-enemy-planes-approaching = Enemy planes approaching.
notification-nuclear-weapon-available = Nuclear weapon available.
notification-nuclear-weapon-launched = Nuclear weapon launched.
notification-nuclear-warhead-approaching = Nuclear warhead approaching.

actor-fact =
    .name = Construction Yard
    .description = Builds structures.
    .encyclopedia =
    The core of any base. It produces buildings and defenses while also providing a build radius. It has a large health pool, but is difficult to replace, making it paramount to defend.

    It can be unpacked into a mobile version using the deploy key, but takes a small amount of time to do so.

actor-fact-gdi-name = GDI Construction Yard
actor-fact-nod-name = Nod Construction Yard

actor-nuke =
    .name = Power Plant
    .description = Generates power.
    .encyclopedia =
    Provides power to the structures in your base. Power output is directly related to the power plant's condition, so protect them during battles.

actor-nuk2 =
    .name = Advanced Power Plant
    .description =
    Provides more power, cheaper than the
    standard Power Plant.
    .encyclopedia =
    Provides more power to the structures in your base, making it more cost-effective than the Power Plant. Power output is directly related to its condition, so protect it during battle.

actor-proc =
    .name = Tiberium Refinery
    .description =
    Processes raw Tiberium
    into usable resources.
    .encyclopedia =
    Stores and processes the alien material and resource, Tiberium. Harvesters collect Tiberium in the field and deposit it at the refinery, where it is converted into credits.

    The refinery stores Tiberium and immediately deploys a harvester once constructed. 3 harvesters is the maximum it can handle for close refining.

actor-silo =
    .name = Tiberium Silo
    .description = Stores processed Tiberium.
    .encyclopedia =
    Stores credits. If storage is full, harvesters will wait until credits are spent.

    Useful when harvesting the more valuable blue Tiberium, as a single refinery cannot store a full load.

actor-pyle =
    .name = Barracks
    .description = Trains infantry.
    .encyclopedia =
    Produces infantry for GDI. Once unlocked, advanced GDI infantry are hard-hitting and fast.

actor-hand =
    .name = Hand of Nod
    .description = Trains infantry.
    .encyclopedia =
    Produces infantry for Nod. Once unlocked, advanced Nod infantry are slow but have high health pools.

actor-afld =
    .name = Airstrip
    .description =
    Provides a dropzone
    for vehicle reinforcements.
    .encyclopedia =
    Delivers vehicles by plane for Nod. Nod vehicles are fast but fragile. Produce harvesters at the start to jumpstart your economy.

actor-weap =
    .name = Weapons Factory
    .description = Produces vehicles.
    .encyclopedia =
    Produces vehicles for GDI. GDI vehicles tend to be slow but hard-hitting. Produce harvesters at the start to jumpstart your economy.

actor-hpad =
    .name = Helipad
    .description =
    Produces and repairs helicopters.
    .encyclopedia =
    Produces and repairs helicopters. Requires a Communications Center to build advanced attack helicopters.

actor-hq =
    .name = Communications Center
    .description =
    Provides radar and Air Strike support power.
    Unlocks higher-tech units and buildings.
    Requires power to operate.
    .airstrikepower-name = Air Strike
    .airstrikepower-description = Deploy an aerial napalm strike.
    Burns buildings and infantry along a line.
    .encyclopedia =
    Grants the player access to the minimap (top right) when there is sufficient power. Unlocks new units while also granting access to the airstrike support power.

actor-fix =
    .name = Repair Facility
    .description = Repairs vehicles.
    .encyclopedia =
    Repairs vehicles. The repair command can be used on units to send them to the nearest repair facility.

actor-eye =
    .name = Advanced Communications Center
    .description =
    Provides radar and Orbital Ion Cannon support power.
    Unlocks Mammoth Tank and Commando.
    Requires power to operate.
    .ioncannonpower-name = Ion Cannon
    .ioncannonpower-description = Initiates an Ion Cannon strike.
    Applies instant damage to a small area.
    .encyclopedia =
    Unlocks advanced units and the ion cannon support power. If the Communications Center is lost, it restores minimap functionality and unit production.

actor-tmpl =
    .name = Temple of Nod
    .description =
    Provides Nuclear Strike support power.
    Unlocks Stealth Tank, Chem. Warrior and Obelisk of Light.
    Requires power to operate.
    .nukepower-name = Nuclear Strike
    .nukepower-description = Launches a tactical nuclear warhead.
    Applies heavy damage over a large area.
    .encyclopedia =
    Unlocks advanced units and the Nuke support power. Keeps unit unlocks online if the Communications Center is lost.

actor-gun =
    .name = Turret
    .description =
    Basic Anti-Tank base defense.
      Strong vs Tanks and Vehicles
      Weak vs Infantry
    .encyclopedia =
    Base defense armed with an armor-piercing cannon, it deals significant damage to vehicles in range.

actor-sam =
    .name = SAM Site
    .description =
    Anti-Aircraft base defense.
      Strong vs Aircraft
      Cannot target Ground units
    .encyclopedia =
    Nod anti-air base defense. It remains protected when closed and opens to engage aircraft.

actor-obli =
    .name = Obelisk of Light
    .description =
    Advanced base defense.
    Requires power to operate.
      Strong vs all Ground units
      Cannot target Aircraft
    .encyclopedia =
    Advanced defense for Nod that quickly destroys ground targets with a powerful laser.

actor-gtwr =
    .name = Guard Tower
    .description =
    Basic defensive structure.
      Strong vs Infantry
      Weak vs Tanks
    .encyclopedia =
    Base defense armed with a high-velocity machine gun, it shreds infantry and light vehicles within range with its large area of effect.

actor-atwr =
    .name = Advanced Guard Tower
    .description =
    All-purpose defensive structure.
      Strong vs Aircraft and Infantry
      Weak vs Tanks
    .encyclopedia =
    Advanced defense for GDI that fires volleys of high explosive missiles at both ground and air targets. Effective versus everything.

actor-sbag =
    .name = Sandbag Barrier
    .description =
    Stops infantry and light vehicles.
      Can be crushed by tanks.
    .encyclopedia =
    Blocks movement of infantry and light vehicles, but can be crushed by tanks. Immune to small arms fire.

    This barrier can be built in multiple segments once an initial sandbag has been placed.

actor-cycl =
    .name = Chain Link Barrier
    .description =
    Stops infantry and light vehicles.
      Can be crushed by tanks.
    .encyclopedia =
    Blocks movement of infantry and light vehicles, but can be crushed by tanks. Immune to small arms fire.

    This barrier can be built in multiple segments once an initial chain link has been placed.

actor-brik =
    .name = Concrete Barrier
    .description =
    Stops infantry and most tanks.
    Blocks some projectiles.
    .encyclopedia =
    Blocks all unit movement, except for Mammoth Tanks which can crush it. Immune to small arms fire.

    This barrier can be built in multiple segments once an initial wall has been placed.

actor-barracks-name = Infantry Production
actor-vehicleproduction-name = Vehicle Production
actor-anypower-name = Power Generation
actor-anyhq-name = Communications Center

## tech.yaml
actor-v19-husk-name = Oil Derrick (Destroyed)
actor-hosp-husk-name = Hospital (Destroyed)
actor-bio-husk-name = Biological Lab (Destroyed)

actor-hosp =
    .name = Hospital
    .captured-desc = Provides infantry with self-healing.
    .capturable-desc = Capture to enable self-healing for infantry.
    .encyclopedia =
    Heals friendly infantry over time when captured.

actor-miss =
    .name = Tech Center
    .captured-desc = Provides range of vision.
    .capturable-desc = Capture to give visual range.
    .encyclopedia =
    Provides a wide area of vision when captured.

actor-bio =
    .name = Biological Lab
    .captured-desc = Provides infantry with Tiberium immunity.
    .capturable-desc = Capture to enable Tiberium immunity for infantry.
    .encyclopedia =
    Grants friendly infantry immunity to Tiberium when captured.

actor-v19 =
    .name = Oil Derrick
    .captured-desc = Provides additional funds.
    .capturable-desc = Capture to receive additional funds.
    .encyclopedia =
    Produces a small amount of income frequently when captured. Makes less money than building a harvester, but provides consistent income.

## trees.yaml
actor-splitblue-name = Blue Blossom Tree
actor-t03-transformable-name = (Tree that can transform into a Blossom Tree)
actor-t13-transformable-name = (Tree that can transform into a Blossom Tree)

## vehicles.yaml
actor-mcv =
    .name = Mobile Construction Vehicle
    .description =
    Deploys into a Construction Yard.
      Unarmed
    .encyclopedia =
    Deploying an MCV changes it into a Construction Yard. The MCV has more health in its deployed form, but undeploying can be helpful to escape infantry.

    If you have a teammate, they can “gift” you an MCV by destroying an undeployed one near your base, allowing you to reclaim it with an engineer.

actor-harv =
    .name = Harvester
    .generic-name = Harvester
    .description =
    Collects Tiberium for processing.
      Unarmed
    .encyclopedia =
    Harvesters slowly mine Tiberium and deposit it in your refinery, where it is converted into credits. They can also be sent to allied refineries to provide funds.

notification-harvester-lost = Harvester lost.

actor-apc =
    .name = APC
    .description =
    Armed infantry transport.
    Can attack Aircraft.
      Strong vs Vehicles
      Weak vs Infantry
    .encyclopedia =
    The APC (Armored Personnel Carrier) transports infantry, making it handy for capturing civilian structures. While its flak gun does not do much damage to enemy vehicles, the APC can resist enemy shots while your Hum-vees deal the damage.

    It also functions as your primary AA unit; if you hit the stop command, it'll retarget onto any nearby enemy air units.

actor-arty =
    .name = Artillery
    .description =
    Long-range artillery.
      Strong vs Infantry, Vehicles and Buildings
    .encyclopedia =
    Nod's artillery is a glass cannon, fragile but dealing large amounts of damage at long range. Particularly strong vs structures and infantry, it can destroy stationary tanks.

    Highly vulnerable when clumped due to its explosion on death.

actor-ftnk =
    .name = Flame Tank
    .description =
    Heavily armored flame-throwing vehicle.
      Strong vs Infantry, Buildings and Vehicles
      Weak vs Tanks
    .encyclopedia =
    Roast infantry and structures alike, making them great for surprise attacks. Be wary of their splash damage on death, but be aware it can also be used to your advantage.

actor-bggy =
    .name = Nod Buggy
    .description =
    Fast scout and anti-infantry vehicle.
      Strong vs Infantry
      Weak vs Tanks
    .encyclopedia =
    Strong versus light vehicles and infantry, Buggies are great at scouting and killing isolated infantry. Though it is a little cheaper and faster than the GDI Hum-vee, they are more fragile.

actor-bike =
    .name = Recon Bike
    .description =
    Fast scout vehicle with rockets.
    Can attack Aircraft.
      Strong vs Vehicles and Tanks
      Weak vs Infantry
    .encyclopedia =
    The Nod bike is a very fast vehicle armed with armor-piercing rockets, which makes it great for hit and run and harvester harassment.

    Be wary that its rockets are slow and have a hard time hitting fast-moving targets.

actor-jeep =
    .name = Hum-vee
    .description =
    Fast scout and anti-infantry vehicle.
      Strong vs Infantry
      Weak vs Tanks
    .encyclopedia =
    Strong versus light vehicles and infantry, Hum-vees are great at scouting and eliminating isolated infantry.

    They're excellent for dealing with Nod bike spam and should be built instead of tanks in such engagements. Although a little slower than the Nod buggy, it has more health.

actor-ltnk =
    .name = Light Tank
    .description =
    Fast, light tank.
      Strong vs Vehicles and Tanks
      Weak vs Infantry
    .encyclopedia =
    With its great speed and high health pool, the light tank works great as a frontline unit in various compositions despite its damage output.

    It should be combined with bikes, stealth tanks, or infantry when facing medium tanks.

actor-mtnk =
    .name = Medium Tank
    .description =
    General-purpose GDI Tank.
      Strong vs Tanks and Vehicles
      Weak vs Infantry
    .encyclopedia =
    The workhorse of GDI, offering a good balance of speed, durability and firepower. It will easily destroy Nod light tanks and can tear through structures if it sneaks into a base.

    Mix minigunners with your medium tank armies to deal with enemy infantry.

actor-htnk =
    .name = Mammoth Tank
    .description =
    Heavily armored GDI Tank.
    Can attack Aircraft.
      Strong vs Everything
    .encyclopedia =
    A crawling battle station, capable of self-repair and engaging any threat. It is particularly effective against enemy armor, but can also destroy small amounts of infantry or aircraft.

    When in an enemy base, put it on aggressive stance to have it carve a path of destruction as it moves.

actor-msam =
    .name = Rocket Launcher
    .description =
    Long-range rocket artillery.
      Strong vs all ground units
    .encyclopedia =
    Referred to as  “MLRS” by players (Multiple Launch Rocket System), this artillery platform fires volleys of rockets at distant targets. It is particularly effective against light vehicles and does moderate damage vs infantry, buildings, and heavy armor.

    A strong unit to build vs enemy artillery, its high bullet velocity and damage to light vehicles makes it the perfect counter to Nod artillery.

actor-mlrs =
    .name = Mobile SAM
    .description =
    Powerful anti-air unit.
      Cannot attack ground units.
    .encyclopedia =
    Generally referred to as “MSAM” by players, this Nod vehicle is a dedicated anti-air vehicle when their bikes do not cut it. It fires slow-moving missiles with powerful splash damage.

actor-stnk =
    .name = Stealth Tank
    .description =
    Long-range missile tank that can cloak.
    Can attack Aircraft.
    Has weak armor. Can be spotted by infantry and
    defense structures.
      Strong vs Vehicles and Tanks
      Weak vs Infantry
    .encyclopedia =
    Cloaked units that become visible when damaged or firing their weapons. They can also be detected by defenses within a small radius or infantry at a one-cell range.

    Their long-range AP missiles are excellent for dealing with enemy armor or air. They can also crush infantry in a pinch.

actor-mhq =
    .name = Mobile HQ
    .description =
    Mobile base of operations.

actor-truck =
    .name = Supply Truck
    .description =
    Transports cash to other players.
    Builds quickly
      Unarmed
    .encyclopedia =
    Supply trucks are a convenient way to share cash when your ally is out of money, or you can't spend yours fast enough. They build far quicker than their cost suggests.

## ai.yaml
bot-cabal =
    .name = Cabal

bot-watson =
    .name = Watson

bot-hal9001 =
    .name = HAL 9001
