Container@MUSIC_PANEL:
	Logic: MusicPlayerLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 410
	Height: 435
	Children:
		LogicTicker@SONG_WATCHER:
		Label@TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: label-music-title-panel-title
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				ScrollPanel@MUSIC_LIST:
					X: 15
					Y: 30
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT - 125
					Children:
						ScrollItem@MUSIC_TEMPLATE:
							Width: PARENT_WIDTH - 27
							Height: 25
							X: 2
							Y: 0
							Visible: false
							EnableChildMouseOver: True
							Children:
								LabelWithTooltip@TITLE:
									X: 10
									Width: PARENT_WIDTH - 50
									Height: 25
									TooltipContainer: TOOLTIP_CONTAINER
									TooltipTemplate: SIMPLE_TOOLTIP
								Label@LENGTH:
									Width: 50
									X: PARENT_WIDTH - 60
									Align: Right
									Height: 25
				Container@LABEL_CONTAINER:
					X: 25
					Y: 4
					Width: PARENT_WIDTH - 30
					Children:
						Label@TITLE:
							Width: 100
							Height: 25
							Text: label-container-title
							Align: Center
							Font: Bold
						Label@TYPE:
							X: PARENT_WIDTH - 85
							Height: 25
							Width: 50
							Text: label-music-controls-length
							Align: Right
							Font: Bold
				Container@BUTTONS:
					X: 15
					Y: PARENT_HEIGHT - HEIGHT - 40
					Width: PARENT_WIDTH - 30
					Children:
						Button@BUTTON_PREV:
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PREV:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: prev
						Button@BUTTON_PLAY:
							X: 35
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PLAY:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: play
						Button@BUTTON_PAUSE:
							Visible: false
							X: 35
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_PAUSE:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: pause
						Button@BUTTON_STOP:
							X: 70
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_STOP:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: stop
						Button@BUTTON_NEXT:
							X: 105
							Width: 26
							Height: 26
							IgnoreChildMouseOver: true
							Children:
								Image@IMAGE_NEXT:
									X: 5
									Y: 5
									Width: 16
									Height: 16
									ImageCollection: music
									ImageName: next
						ExponentialSlider@MUSIC_SLIDER:
							X: PARENT_WIDTH - WIDTH
							Y: 3
							Width: PARENT_WIDTH - 145
							Height: 20
							Ticks: 7
				Label@TIME_LABEL:
					X: (PARENT_WIDTH - WIDTH) / 2
					Y: PARENT_HEIGHT - HEIGHT - 60 - 3
					Width: 140
					Height: 25
					Align: Center
				Checkbox@SHUFFLE:
					X: 15
					Y: PARENT_HEIGHT - HEIGHT - 60
					Width: 85
					Height: 20
					Font: Regular
					Text: checkbox-music-controls-shuffle
				Checkbox@REPEAT:
					X: PARENT_WIDTH - 15 - WIDTH
					Y: PARENT_HEIGHT - HEIGHT - 60
					Width: 70
					Height: 20
					Font: Regular
					Text: checkbox-music-controls-loop
				Container@NO_MUSIC_LABEL:
					X: 15
					Y: (PARENT_HEIGHT - HEIGHT - 60) / 2
					Width: PARENT_WIDTH - 30
					Height: 75
					Visible: false
					Children:
						Label@TITLE:
							Width: PARENT_WIDTH - 24
							Height: 25
							Font: Bold
							Align: Center
							Text: label-no-music-title
						Label@DESCA:
							Y: 20
							Width: PARENT_WIDTH - 24
							Height: 25
							Align: Center
							Text: label-no-music-desc-a
						Label@DESCB:
							Y: 40
							Width: PARENT_WIDTH - 24
							Height: 25
							Align: Center
							Text: label-no-music-desc-b
		Button@BACK_BUTTON:
			Key: escape
			X: 0
			Y: PARENT_HEIGHT - 1
			Width: 140
			Height: 35
			Text: button-back
		Label@MUTE_LABEL:
			X: 100
			Y: PARENT_HEIGHT - 60 - 3
			Width: 300
			Height: 20
			Font: Small
		TooltipContainer@TOOLTIP_CONTAINER:
