Player:
	PlayerResources:
		DefaultCash: 4000

World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, nod02b.lua
	MusicPlaylist:
		StartingMusic: ind2
		VictoryMusic: nod_win1
	MissionData:
		Briefing: GDI has kept a stranglehold on Egypt for many years. Establish a forward attack base in the region. Begin by selecting your Mobile Construction Vehicle (MCV) and right-clicking on it to enable base construction. This area contains plenty of Tiberium, so establishing a base should be straightforward.
		BriefingVideo: nod2.vqa
		StartVideo: seige.vqa
		WinVideo: airstrk.vqa
		LossVideo: deskill.vqa

^Vehicle:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false
	RenderSprites:
		PlayerPalette: player-units

^Tank:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false
	RenderSprites:
		PlayerPalette: player-units

^Helicopter:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false
	RenderSprites:
		PlayerPalette: player-units

^Infantry:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false
	RenderSprites:
		PlayerPalette: player-units

^Plane:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false

^Ship:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false

^Building:
	Tooltip:
		GenericVisibility: Enemy
		ShowOwnerRow: false

^Wall:
	Tooltip:
		ShowOwnerRow: false

^CommonHuskDefaults:
	Tooltip:
		GenericVisibility: Enemy, Ally, Neutral
		GenericStancePrefix: false
		ShowOwnerRow: false
	RenderSprites:
		PlayerPalette: player-units

NUK2:
	Buildable:
		Prerequisites: ~disabled

GUN:
	Buildable:
		Prerequisites: ~disabled

CYCL:
	Buildable:
		Prerequisites: ~disabled

FIX:
	Buildable:
		Prerequisites: ~disabled

HPAD:
	Buildable:
		Prerequisites: ~disabled

OBLI:
	Buildable:
		Prerequisites: ~disabled

BRIK:
	Buildable:
		Prerequisites: ~disabled

TMPL:
	Buildable:
		Prerequisites: ~disabled

FTNK:
	Buildable:
		Prerequisites: ~disabled

STNK:
	Buildable:
		Prerequisites: ~disabled

ARTY:
	Buildable:
		Prerequisites: ~disabled

E5:
	Buildable:
		Prerequisites: ~disabled

RMBO:
	Buildable:
		Prerequisites: ~disabled

MLRS:
	Buildable:
		Prerequisites: ~disabled

MCV:
	Buildable:
		Prerequisites: ~disabled
	RenderSprites:
		PlayerPalette: player

MCV.Husk:
	RenderSprites:
		PlayerPalette: player

HARV:
	RenderSprites:
		PlayerPalette: player

HARV.Husk:
	RenderSprites:
		PlayerPalette: player

SAM:
	Buildable:
		Prerequisites: ~disabled

HQ:
	Buildable:
		Prerequisites: ~disabled

AFLD:
	Buildable:
		Prerequisites: ~disabled

E4:
	Buildable:
		Prerequisites: ~disabled

E3:
	Buildable:
		Prerequisites: ~disabled

E2:
	Buildable:
		Prerequisites: ~disabled

SBAG:
	Buildable:
		Prerequisites: ~disabled

GTWR:
	Buildable:
		Prerequisites: ~disabled

WEAP:
	Buildable:
		Prerequisites: ~disabled

EYE:
	Buildable:
		Prerequisites: ~disabled

ATWR:
	Buildable:
		Prerequisites: ~disabled
