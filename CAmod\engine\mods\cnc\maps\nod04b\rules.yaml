World:
	LuaScript:
		Scripts: campaign.lua, utils.lua, nod04b.lua
	MusicPlaylist:
		StartingMusic: valkyrie
		VictoryMusic: nod_win1
	MissionData:
		Briefing: A small village friendly to our cause has been increasingly harassed by GDI, and the Brotherhood wishes you to assist them in their efforts.\n\nSeek out the enemy village and destroy it. The event will be disguised as a GDI attack.
		BriefingVideo: nod4b.vqa
		StartVideo: retro.vqa
		LossVideo: deskill.vqa
	SmudgeLayer@SCORCH:
		InitialSmudges:
			37,24: sc6,0
			36,18: sc6,0

Player:
	EnemyWatcher:
	PlayerResources:
		DefaultCash: 0

^Palettes:
	IndexedPlayerPalette:
		PlayerIndex:
			NodSupporter: 192, 164, 132, 155, 133, 197, 112, 12, 163, 132, 155, 133, 134, 197, 154, 198
	IndexedPlayerPalette@units:
		PlayerIndex:
			NodSupporter: 192, 164, 132, 155, 133, 197, 112, 12, 163, 132, 155, 133, 134, 197, 154, 198

^Infantry:
	AnnounceOnSeen:

^CivBuilding:
	AnnounceOnSeen:

^CivBuildingHusk:
	AnnounceOnSeen:

NUK2:
	Buildable:
		Prerequisites: ~disabled

GUN:
	Buildable:
		Prerequisites: ~disabled

CYCL:
	Buildable:
		Prerequisites: ~disabled

FIX:
	Buildable:
		Prerequisites: ~disabled

HPAD:
	Buildable:
		Prerequisites: ~disabled

OBLI:
	Buildable:
		Prerequisites: ~disabled

BRIK:
	Buildable:
		Prerequisites: ~disabled

TMPL:
	Buildable:
		Prerequisites: ~disabled

FTNK:
	Buildable:
		Prerequisites: ~disabled

STNK:
	Buildable:
		Prerequisites: ~disabled

ARTY:
	Buildable:
		Prerequisites: ~disabled

E5:
	Buildable:
		Prerequisites: ~disabled

RMBO:
	Buildable:
		Prerequisites: ~disabled

MLRS:
	Buildable:
		Prerequisites: ~disabled

MCV:
	Buildable:
		Prerequisites: ~disabled

SAM:
	Buildable:
		Prerequisites: ~disabled

AFLD:
	Buildable:
		Prerequisites: ~disabled

E4:
	Buildable:
		Prerequisites: ~disabled

SBAG:
	Buildable:
		Prerequisites: ~disabled

GTWR:
	Buildable:
		Prerequisites: ~disabled

WEAP:
	Buildable:
		Prerequisites: ~disabled

EYE:
	Buildable:
		Prerequisites: ~disabled

ATWR:
	Buildable:
		Prerequisites: ~disabled
