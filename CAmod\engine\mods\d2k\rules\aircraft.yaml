carryall.reinforce:
	Inherits: ^Plane
	Valued:
		Cost: 1100
	UpdatesPlayerStatistics:
		AddToAssetsValue: false
	Tooltip:
		Name: actor-carryall-reinforce.name
	Health:
		HP: 48000
	Armor:
		Type: light
	Aircraft:
		CruiseAltitude: 2160
		CruisingCondition: cruising
		Speed: 144
		TurnSpeed: 16
		LandableTerrainTypes: Sand, Rock, Transition, Spice, SpiceSand, Dune, Concrete
		Repulsable: False
		AirborneCondition: airborne
		CanForceLand: false
		CanSlide: True
		VTOL: true
		IdleTurnSpeed: 4
	Targetable@GROUND:
		TargetTypes: Ground, Vehicle
		RequiresCondition: !airborne
	Targetable@AIRBORNE:
		TargetTypes: Air
		RequiresCondition: airborne
	SpawnActorOnDeath@CRUISING:
		Actor: carryall.husk
		RequiresCondition: cruising
	SpawnActorOnDeath@LANDING:
		Actor: carryall.huskVTOL
		RequiresCondition: !cruising
	Carryall:
		BeforeLoadDelay: 10
		BeforeUnloadDelay: 15
		LocalOffset: 0, 0, -128
	RenderSprites:
		Image: carryall
	ChangesHealth:
		Step: 50
		Delay: 3
		StartIfBelow: 50
	Buildable:
		BuildDuration: 750
		BuildDurationModifier: 100
		Description: actor-carryall-reinforce.description

carryall:
	Inherits: carryall.reinforce
	UpdatesPlayerStatistics:
		AddToAssetsValue: true
	-Carryall:
	AutoCarryall:
		BeforeLoadDelay: 10
		BeforeUnloadDelay: 15
		LocalOffset: 0, 0, -128
	Encyclopedia:
		Description: actor-carryall-encyclopedia
		Order: 230
		Category: Units
	Aircraft:
		MinAirborneAltitude: 400
	RevealsShroud@lifting_low:
		Range: 2c512
		Type: GroundPosition
		RequiresCondition: !airborne
	RevealsShroud@lifting_high:
		Range: 1c256
		Type: GroundPosition
		RequiresCondition: !cruising
	Buildable:
		Queue: Aircraft
		BuildPaletteOrder: 120

frigate:
	Inherits: ^Plane
	ParaDrop:
		DropRange: 1c0
	Interactable:
	Tooltip:
		Name: actor-frigate-name
	Aircraft:
		IdleBehavior: LeaveMap
		Speed: 189
		TurnSpeed: 4
		Repulsable: False
		MaximumPitch: 20
		CruiseAltitude: 2048
		VTOL: true
		CanHover: true
		CanSlide: true
	-AppearsOnRadar:
	Cargo:
		MaxWeight: 20
	RejectsOrders:

ornithopter:
	Inherits: ^Plane
	Buildable:
		Prerequisites: upgrade.hightech
	AttackBomber:
		FacingTolerance: 8
	Armament:
		Weapon: OrniBomb
	Health:
		HP: 9000
	Armor:
		Type: light
	Encyclopedia:
		Description: actor-ornithopter.encyclopedia
		Order: 240
		Category: Units
	Aircraft:
		Speed: 224
		TurnSpeed: 8
		Repulsable: False
		CruiseAltitude: 1920
	Targetable:
		TargetTypes: Air
	AmmoPool:
		Ammo: 5
	Tooltip:
		Name: actor-ornithopter.name
	SpawnActorOnDeath:
		Actor: ornithopter.husk
	RejectsOrders:
	RevealOnFire:
	-MapEditorData:

ornithopter.husk:
	Inherits: ^AircraftHusk
	Tooltip:
		Name: actor-ornithopter-husk-name
	Aircraft:
		TurnSpeed: 20
		Speed: 224
	RenderSprites:
		Image: ornithopter

carryall.husk:
	Inherits: ^AircraftHusk
	Tooltip:
		Name: actor-carryall-husk-name
	Aircraft:
		TurnSpeed: 16
		Speed: 144
		CanSlide: True
		VTOL: true
	RenderSprites:
		Image: carryall

carryall.huskVTOL:
	Inherits: ^AircraftHusk
	Tooltip:
		Name: actor-carryall-huskvtol-name
	FallsToEarth:
		Moves: False
		Velocity: 0c128
	Aircraft:
		TurnSpeed: 16
		CanSlide: True
		VTOL: true
	RenderSprites:
		Image: carryall
