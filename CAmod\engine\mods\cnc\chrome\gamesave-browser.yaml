Container@GAMESAVE_BROWSER_PANEL:
	Logic: GameSaveBrowserLogic
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 716
	Height: 435
	Children:
		Label@LOAD_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: label-gamesave-browser-panel-load-title
			Visible: False
		Label@SAVE_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
			Text: label-gamesave-browser-panel-save-title
			Visible: False
		Background@bg:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				ScrollPanel@GAME_LIST:
					X: 15
					Y: 15
					Width: PARENT_WIDTH - 30
					Height: PARENT_HEIGHT - 30
					Children:
						ScrollItem@NEW_TEMPLATE:
							Width: PARENT_WIDTH - 27
							Height: 25
							X: 2
							Visible: false
							Children:
								Label@TITLE:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Align: Center
									Text: label-bg-title
						ScrollItem@GAME_TEMPLATE:
							Width: PARENT_WIDTH - 27
							Height: 25
							X: 2
							Visible: false
							EnableChildMouseOver: True
							Children:
								LabelWithTooltip@TITLE:
									X: 10
									Width: PARENT_WIDTH - 200 - 10
									Height: 25
									TooltipContainer: GAMESAVE_TOOLTIP_CONTAINER
									TooltipTemplate: SIMPLE_TOOLTIP
								Label@DATE:
									X: PARENT_WIDTH - WIDTH - 10
									Width: 200
									Height: 25
									Align: Right
				Container@SAVE_WIDGETS:
					X: 15
					Y: PARENT_HEIGHT - 40
					Width: PARENT_WIDTH - 30
					Height: 30
					Visible: False
					Children:
						TextField@SAVE_TEXTFIELD:
							Width: PARENT_WIDTH
							Height: 25
							Type: Filename
				Button@CANCEL_BUTTON:
					Key: escape
					X: 0
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-back
				Button@DELETE_ALL_BUTTON:
					X: PARENT_WIDTH - 370 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 100
					Height: 35
					Text: button-bg-delete-all
				Button@DELETE_BUTTON:
					X: PARENT_WIDTH - 260 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 100
					Height: 35
					Text: button-bg-delete
					Key: Delete
				Button@RENAME_BUTTON:
					X: PARENT_WIDTH - 150 - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 100
					Height: 35
					Text: button-bg-rename
					Key: F2
				Button@LOAD_BUTTON:
					Key: return
					X: PARENT_WIDTH - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-load
					Visible: False
				Button@SAVE_BUTTON:
					Key: return
					X: PARENT_WIDTH - WIDTH
					Y: PARENT_HEIGHT - 1
					Width: 140
					Height: 35
					Text: button-bg-save
					Visible: False
		TooltipContainer@GAMESAVE_TOOLTIP_CONTAINER:
