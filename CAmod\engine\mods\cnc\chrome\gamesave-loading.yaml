Container@GAMESAVE_LOADING_SCREEN:
	Logic: GameSaveLoadingLogic
	Width: WINDOW_WIDTH
	Height: WINDOW_HEIGHT
	Children:
		LogicKeyListener@CANCEL_HANDLER:
		Image@NOD:
			X: WINDOW_WIDTH / 2 - 384
			Y: (WINDOW_HEIGHT - 256) / 2
			ImageCollection: logos
			ImageName: nod-load
		Image@GDI:
			X: WINDOW_WIDTH / 2 + 128
			Y: (WINDOW_HEIGHT - 256) / 2
			ImageCollection: logos
			ImageName: gdi-load
		Image@EVA:
			X: WINDOW_WIDTH - 128 - 43
			Y: 43
			Width: 128
			Height: 64
			ImageCollection: logos
			ImageName: eva
		Label@VERSION_LABEL:
			Logic: VersionLabelLogic
			X: WINDOW_WIDTH - 128 - 43
			Y: 116
			Width: 128
			Align: Center
			Shadow: true
		Background@BORDER:
			Width: WINDOW_WIDTH
			Height: WINDOW_HEIGHT
			Background: shellmapborder
		Label@TITLE:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 - 29
			Height: 25
			Font: Bold
			Align: Center
			Text: label-gamesave-loading-screen-title
		ProgressBar@PROGRESS:
			X: (WINDOW_WIDTH - 500) / 2
			Y: 3 * WINDOW_HEIGHT / 4
			Width: 500
			Height: 20
		Label@DESC:
			Width: WINDOW_WIDTH
			Y: 3 * WINDOW_HEIGHT / 4 + 19
			Height: 25
			Font: Regular
			Align: Center
			Text: label-gamesave-loading-screen-desc
