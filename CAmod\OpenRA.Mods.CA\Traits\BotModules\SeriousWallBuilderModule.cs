#region Copyright & License Information
/**
 * Copyright (c) The OpenRA Combined Arms Developers (see CREDITS).
 * This file is part of OpenRA Combined Arms, which is free software.
 * It is made available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of the License,
 * or (at your option) any later version. For more information, see COPYING.
 */
#endregion

using System.Collections.Generic;
using System.Linq;
using OpenRA.Mods.Common;
using OpenRA.Mods.Common.Traits;
using OpenRA.Traits;

namespace OpenRA.Mods.CA.Traits
{
	[Desc("Manages wall building for the serious AI after first infantry unit is built.")]
	public class SeriousWallBuilderModuleInfo : ConditionalTraitInfo
	{
		[Desc("Wall types to consider for building, in order of preference.")]
		public readonly HashSet<string> WallTypes = new HashSet<string> { "CHAIN", "BRIK" };

		[Desc("Minimum distance from base center to build walls.")]
		public readonly int MinWallDistance = 8;

		[Desc("Maximum distance from base center to build walls.")]
		public readonly int MaxWallDistance = 12;

		[Desc("Number of gaps to leave in the wall for units to pass through.")]
		public readonly int GapCount = 2;

		[Desc("Size of each gap in cells.")]
		public readonly int GapSize = 2;

		public override object Create(ActorInitializer init) { return new SeriousWallBuilderModule(init.Self, this); }
	}

	public class SeriousWallBuilderModule : ConditionalTrait<SeriousWallBuilderModuleInfo>, IBotTick
	{
		readonly World world;
		readonly Player player;
		bool wallBuilt = false;
		bool infantryBuilt = false;

		public SeriousWallBuilderModule(Actor self, SeriousWallBuilderModuleInfo info)
			: base(info)
		{
			world = self.World;
			player = self.Owner;
		}

		void IBotTick.BotTick(IBot bot)
		{
			if (IsTraitDisabled)
				return;

			// Check if first infantry unit has been built
			if (!infantryBuilt && HasBuiltInfantry())
			{
				infantryBuilt = true;
				AIUtils.BotDebug("Serious AI: First infantry built, preparing to build walls.");
			}

			// Build walls after infantry is built
			if (infantryBuilt && !wallBuilt)
			{
				var wallType = GetCheapestAvailableWall();
				if (wallType != null)
				{
					BuildWallAroundBase(bot, wallType);
					wallBuilt = true;
					AIUtils.BotDebug("Serious AI: Base walls completed with {0}, {1} gaps left open.", wallType, Info.GapCount);
				}
			}
		}

		bool HasBuiltInfantry()
		{
			// Check if any infantry units have been built by looking for actors that are infantry
			// Infantry units typically have BuildAtProductionType: Soldier
			return world.ActorsHavingTrait<Mobile>()
				.Any(a => a.Owner == player && a.Info.HasTraitInfo<BuildableInfo>() &&
					a.Info.TraitInfo<BuildableInfo>().BuildAtProductionType == "Soldier");
		}

		string GetCheapestAvailableWall()
		{
			var availableWalls = new List<(string Name, int Cost)>();

			foreach (var wallType in Info.WallTypes)
			{
				if (!world.Map.Rules.Actors.ContainsKey(wallType))
					continue;

				var actorInfo = world.Map.Rules.Actors[wallType];

				var buildableInfo = actorInfo.TraitInfoOrDefault<BuildableInfo>();
				if (buildableInfo == null)
					continue;

				// Check if we can build this wall type
				var techTree = player.PlayerActor.Trait<TechTree>();
				if (!techTree.HasPrerequisites(buildableInfo.Prerequisites))
					continue;

				var valuedInfo = actorInfo.TraitInfoOrDefault<ValuedInfo>();
				if (valuedInfo != null)
				{
					availableWalls.Add((wallType, valuedInfo.Cost));
				}
			}

			// Return the cheapest available wall
			return availableWalls.OrderBy(w => w.Cost).FirstOrDefault().Name;
		}

		void BuildWallAroundBase(IBot bot, string wallType)
		{
			var baseCenter = GetBaseCenter();
			if (baseCenter == CPos.Zero)
				return;

			var wallPositions = CalculateWallPositions(baseCenter);
			var gapPositions = CalculateGapPositions(wallPositions);

			// Remove gap positions from wall positions
			wallPositions = wallPositions.Except(gapPositions).ToList();

			// Build walls at calculated positions
			foreach (var pos in wallPositions)
			{
				if (CanPlaceWall(pos, wallType))
				{
					var queue = AIUtils.FindQueues(player, "DefenseSQ").FirstOrDefault() ??
								AIUtils.FindQueues(player, "DefenseMQ").FirstOrDefault();

					if (queue != null)
					{
						bot.QueueOrder(Order.StartProduction(queue.Actor, wallType, 1));
					}
				}
			}
		}

		CPos GetBaseCenter()
		{
			// Find construction yard or use first building as base center
			var constructionYard = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player && 
					(a.Info.Name == "fact" || a.Info.Name == "afac" || a.Info.Name == "sfac"));

			if (constructionYard != null)
				return constructionYard.Location;

			// Fallback to any building
			var anyBuilding = world.ActorsHavingTrait<Building>()
				.FirstOrDefault(a => a.Owner == player);

			return anyBuilding?.Location ?? CPos.Zero;
		}

		List<CPos> CalculateWallPositions(CPos baseCenter)
		{
			var positions = new List<CPos>();
			var distance = Info.MinWallDistance;

			// Create a rough circle of wall positions around the base
			for (var angle = 0; angle < 360; angle += 15) // Every 15 degrees
			{
				var radians = angle * System.Math.PI / 180.0;
				var x = (int)(baseCenter.X + distance * System.Math.Cos(radians));
				var y = (int)(baseCenter.Y + distance * System.Math.Sin(radians));
				var pos = new CPos(x, y);

				if (world.Map.Contains(pos))
					positions.Add(pos);
			}

			return positions;
		}

		List<CPos> CalculateGapPositions(List<CPos> wallPositions)
		{
			var gapPositions = new List<CPos>();
			
			if (wallPositions.Count == 0)
				return gapPositions;

			// Create gaps at regular intervals
			var gapInterval = wallPositions.Count / Info.GapCount;
			
			for (var i = 0; i < Info.GapCount; i++)
			{
				var gapStartIndex = i * gapInterval;
				for (var j = 0; j < Info.GapSize && gapStartIndex + j < wallPositions.Count; j++)
				{
					gapPositions.Add(wallPositions[gapStartIndex + j]);
				}
			}

			return gapPositions;
		}

		bool CanPlaceWall(CPos position, string wallType)
		{
			var actorInfo = world.Map.Rules.Actors[wallType];
			var buildingInfo = actorInfo.TraitInfo<BuildingInfo>();

			return world.CanPlaceBuilding(position, actorInfo, buildingInfo, null);
		}
	}
}
