Container@GAME_INFO_PANEL:
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 570
	Height: 435
	Logic: GameInfoLogic
	Visible: False
	Children:
		Label@TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Text: label-game-info-panel-title
			Align: Center
			Font: BigBold
			Contrast: true
		Container@TAB_CONTAINER_2:
			X: 0 - 140 + 1
			Visible: False
			Children:
				Button@BUTTON1:
					Width: 140
					Height: 35
				Button@BUTTON2:
					Y: 45
					Width: 140
					Height: 35
		Container@TAB_CONTAINER_3:
			X: 0 - 140 + 1
			Visible: False
			Children:
				Button@BUTTON1:
					Width: 140
					Height: 35
				Button@BUTTON2:
					Y: 45
					Width: 140
					Height: 35
				Button@BUTTON3:
					Y: 90
					Width: 140
					Height: 35
		Container@TAB_CONTAINER_4:
			X: 0 - 140 + 1
			Visible: False
			Children:
				But<PERSON>@BUTTON1:
					Width: 140
					Height: 35
				Button@BUTTON2:
					Y: 45
					Width: 140
					Height: 35
				Button@BUTTON3:
					Y: 90
					Width: 140
					Height: 35
				Button@BUTTON4:
					Y: 135
					Width: 140
					Height: 35
		Container@TAB_CONTAINER_5:
			X: 0 - 140 + 1
			Visible: False
			Children:
				Button@BUTTON1:
					Width: 140
					Height: 35
				Button@BUTTON2:
					Y: 45
					Width: 140
					Height: 35
				Button@BUTTON3:
					Y: 90
					Width: 140
					Height: 35
				Button@BUTTON4:
					Y: 135
					Width: 140
					Height: 35
				Button@BUTTON5:
					Y: 180
					Width: 140
					Height: 35
		Background@BACKGROUND:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Background: panel-black
			Children:
				Container@STATS_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
				Container@MAP_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
				Container@OBJECTIVES_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
				Container@DEBUG_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
				Container@CHAT_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
				Container@LOBBY_OPTIONS_PANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
