Container@HOTKEYS_PANEL:
	Logic: HotkeysSettingsLogic
		HotkeyGroups:
			hotkey-group-game-commands:
				Types: OrderGenerator, World, Menu
			hotkey-group-viewport-commands:
				Types: Viewport
			hotkey-group-observer-replay-commands:
				Types: Observer, Replay
			hotkey-group-unit-commands:
				Types: Unit
			hotkey-group-unit-stance-commands:
				Types: Stance
			hotkey-group-production-commands:
				Types: Production, ProductionSlot
			hotkey-group-support-power-commands:
				Types: SupportPower
			hotkey-group-music-commands:
				Types: Music
			hotkey-group-chat-commands:
				Types: Chat
			hotkey-group-control-groups:
				Types: ControlGroups
			hotkey-group-editor-commands:
				Types: Editor
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		Label@FILTER_INPUT_LABEL:
			Width: 103
			Height: 25
			Font: Bold
			Text: label-hotkeys-panel-filter-input
		Text<PERSON>ield@FILTER_INPUT:
			X: 108
			Width: 180
			Height: 25
		Label@CONTEXT_DROPDOWN_LABEL:
			X: PARENT_WIDTH - WIDTH - 195 - 5
			Width: 100
			Height: 25
			Font: Bold
			Text: label-hotkeys-panel-context-dropdown
			Align: Right
		DropDownButton@CONTEXT_DROPDOWN:
			X: PARENT_WIDTH - WIDTH
			Width: 195
			Height: 25
			Font: Bold
		ScrollPanel@HOTKEY_LIST:
			Y: 35
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT - 65 - 35
			TopBottomSpacing: 5
			ItemSpacing: 5
			Children:
				Container@HEADER:
					Width: PARENT_WIDTH - 24 - 10
					Height: 18
					Children:
						Background@BACKGROUND:
							Width: PARENT_WIDTH
							Height: 13
							Background: separator
							ClickThrough: True
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: 13
							Font: TinyBold
							Align: Center
				Container@TEMPLATE:
					Width: (PARENT_WIDTH - 24) / 2 - 10
					Height: 30
					Visible: false
					Children:
						Label@FUNCTION:
							Y: 0 - 1
							Width: PARENT_WIDTH - 90 - 5
							Height: 25
							Align: Right
						Button@HOTKEY:
							X: PARENT_WIDTH - WIDTH
							Width: 90
							Height: 25
							TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
		Container@HOTKEY_EMPTY_LIST:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Visible: false
			Children:
				Label@HOTKEY_EMPTY_LIST_MESSAGE:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Align: Center
					Text: label-hotkey-empty-list-message
		Background@HOTKEY_REMAP_BGND:
			Y: PARENT_HEIGHT - HEIGHT - 1
			Width: PARENT_WIDTH
			Height: 65 + 1
			Background: panel-gray
			Children:
				Container@HOTKEY_REMAP_DIALOG:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT
					Children:
						Label@HOTKEY_LABEL:
							X: 15
							Y: 19
							Width: 200
							Height: 25
							Font: Bold
							Align: Right
						HotkeyEntry@HOTKEY_ENTRY:
							X: 15 + 200 + 5
							Y: 20
							Width: 220
							Height: 25
						Container@NOTICES:
							X: 15 + 200 + 5
							Y: 42
							Width: 220
							Height: 25
							Children:
								Label@ORIGINAL_NOTICE:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Font: Tiny
								Label@DUPLICATE_NOTICE:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Font: Tiny
								Label@READONLY_NOTICE:
									Width: PARENT_WIDTH
									Height: PARENT_HEIGHT
									Font: Tiny
									Text: label-notices-readonly-notice
						Button@OVERRIDE_HOTKEY_BUTTON:
							X: PARENT_WIDTH - 3 * WIDTH - 30
							Y: 20
							Width: 70
							Height: 25
							Text: button-hotkey-remap-dialog-override
							Font: Bold
						Button@CLEAR_HOTKEY_BUTTON:
							X: PARENT_WIDTH - 2 * WIDTH - 30
							Y: 20
							Width: 65
							Height: 25
							Text: button-hotkey-remap-dialog-clear.label
							Font: Bold
							TooltipText: button-hotkey-remap-dialog-clear.tooltip
							TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
						Button@RESET_HOTKEY_BUTTON:
							X: PARENT_WIDTH - WIDTH - 20
							Y: 20
							Width: 65
							Height: 25
							Text: button-hotkey-remap-dialog-reset.label
							Font: Bold
							TooltipText: button-hotkey-remap-dialog-reset.tooltip
							TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
							TooltipTemplate: SIMPLE_TOOLTIP
