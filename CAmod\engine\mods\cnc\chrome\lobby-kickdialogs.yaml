Background@KICK_CLIENT_DIALOG:
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Logic: KickClientLogic
	Background: scrollpanel-bg
	Children:
		Label@TITLE:
			Y: 41
			Width: PARENT_WIDTH
			Height: 25
			Font: Bold
			Align: Center
		Label@TEXTA:
			Y: 68
			Width: PARENT_WIDTH
			Height: 25
			Font: Regular
			Align: Center
			Text: label-kick-client-dialog-text-a
		Label@TEXTB:
			Y: 86
			Width: PARENT_WIDTH
			Height: 25
			Font: Regular
			Align: Center
			Text: label-kick-client-dialog-text-b
		Checkbox@PREVENT_REJOINING_CHECKBOX:
			X: (PARENT_WIDTH - WIDTH) / 2
			Y: 120
			Width: 150
			Height: 20
			Font: Regular
			Text: checkbox-kick-client-dialog-prevent-rejoining
		<PERSON><PERSON>@OK_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 + 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-kick-client-dialog
			Font: Bold
		But<PERSON>@CANCEL_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 - 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-cancel
			Font: Bold

Background@KICK_SPECTATORS_DIALOG:
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Logic: KickSpectatorsLogic
	Background: scrollpanel-bg
	Children:
		Label@TITLE:
			Y: 41
			Width: PARENT_WIDTH
			Height: 25
			Font: Bold
			Align: Center
			Text: label-kick-spectators-dialog-title
		Label@TEXT:
			Y: 86
			Width: PARENT_WIDTH
			Height: 25
			Font: Regular
			Align: Center
		Button@OK_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 + 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-kick-spectators-dialog-ok
			Font: Bold
		Button@CANCEL_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 - 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-cancel
			Font: Bold

Background@FORCE_START_DIALOG:
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Background: scrollpanel-bg
	Children:
		Label@TITLE:
			Y: 41
			Width: PARENT_WIDTH
			Height: 25
			Font: Bold
			Align: Center
			Text: label-force-start-dialog-title
		Label@TEXTA:
			Y: 68
			Width: PARENT_WIDTH
			Height: 25
			Font: Regular
			Align: Center
			Text: label-force-start-dialog-text-a
		Label@TEXTB:
			Y: 86
			Width: PARENT_WIDTH
			Height: 25
			Font: Regular
			Align: Center
			Text: label-force-start-dialog-text-b
		Container@KICK_WARNING:
			Width: PARENT_WIDTH
			Children:
				Label@KICK_WARNING_A:
					X: 0
					Y: 107
					Width: PARENT_WIDTH
					Height: 25
					Font: Bold
					Align: Center
					Text: label-kick-warning-a
				Label@KICK_WARNING_B:
					X: 0
					Y: 124
					Width: PARENT_WIDTH
					Height: 25
					Font: Bold
					Align: Center
					Text: label-kick-warning-b
		Button@OK_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 + 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-force-start-dialog-start
			Font: Bold
		Button@CANCEL_BUTTON:
			X: (PARENT_WIDTH - WIDTH) / 2 - 75
			Y: 155
			Width: 120
			Height: 25
			Text: button-cancel
			Font: Bold
