GenericVoice:
	Variants:
		nod: .v01, .v03
		gdi: .v01, .v03
	Voices:
		Select: ackno, await1, ready, report1, yessir1
		Action: affirm1, ritaway, ugotit, roger, movout1
		Attack: affirm1, ritaway, ugotit, noprob, yessir1
		Die: nuyell1, nuyell3, nuyell4, nuyell5, nuyell6
		Burned: yell1
		Zapped: nuyell3
		Poisoned: nuyell12
	DisableVariants: <PERSON>, <PERSON><PERSON>, <PERSON>ap<PERSON>, Poisoned

VehicleVoice:
	Variants:
		nod: .v00, .v02
		gdi: .v00, .v02
	Voices:
		Select: vehic1, yessir1, await1, unit1, report1
		Action: ackno, affirm1, movout1
		Attack: ackno, affirm1, yessir1
		Unload: movout1, affirm1

CivilianMaleVoice:
	Voices:
		Select: guyyeah1
		Action: guyokay1
		Die: nuyell1, nuyell3, nuyell4, nuyell5, nuyell6
		Burned: yell1
		Zapped: nuyell3
		Poisoned: nuyell12

CivilianFemaleVoice:
	Voices:
		Select: girlyeah
		Action: girlokay
		Die: nuyell1, nuyell3, nuyell4, nuyell5, nuyell6
		Burned: yell1
		Zapped: nuyell3
		Poisoned: nuyell12

MoebiusVoice:
	Voices:
		Select: mcomnd1, mhello1, myes1, mhmmm1
		Action: myesyes1, mcourse1, mthanks1, mtiber1, mplan3
		Die: nuyell1, nuyell3, nuyell4, nuyell5, nuyell6
		Burned: yell1
		Zapped: nuyell3
		Poisoned: nuyell12

CommandoVoice:
	Voices:
		Select: yeah1, yes1, yo1
		Action: onit1, gotit1, noprblm1
		Demolish: bombit1
		Die: ramyell1
		Burned: ramyell1
		Zapped: ramyell1
		Poisoned: ramyell1
		Build: rokroll1, cmon1
		Kill: keepem1, laugh1, lefty1, tuffguy1

DinoVoice:
	Voices:
		Select: dinoyes
		Move: dinomout
		Attack: dinoatk1
		Die: dinodie1
		Burned: dinodie1
		Zapped: dinodie1
		Poisoned: dinodie1
