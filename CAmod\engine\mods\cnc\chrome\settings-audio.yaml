Container@AUDIO_PANEL:
	Logic: AudioSettingsLogic
	Width: PARENT_WIDTH
	Height: PARENT_HEIGHT
	Children:
		ScrollPanel@SETTINGS_SCROLLPANEL:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			CollapseHiddenChildren: True
			TopBottomSpacing: 5
			ItemSpacing: 10
			Children:
				Background@AUDIO_SECTION_HEADER:
					X: 5
					Width: PARENT_WIDTH - 24 - 10
					Height: 13
					Background: separator
					ClickThrough: True
					Children:
						Label@LABEL:
							Width: PARENT_WIDTH
							Height: PARENT_HEIGHT
							Font: TinyBold
							Align: Center
							Text: label-audio-section-header
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@NO_AUDIO_DEVICE_CONTAINER:
							X: 10
							Width: PARENT_WIDTH - 10
							Children:
								Label@NO_AUDIO_DEVICE:
									Width: PARENT_WIDTH
									Height: 20
									Align: Center
									Text: label-no-audio-device-container
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@CASH_TICKS_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@CASH_TICKS:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-cash-ticks-container
						Container@MUTE_SOUND_CONTAINER:
							X: 10
							Y: 30
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@MUTE_SOUND:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-mute-sound-container
						Container@SOUND_VOLUME_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@SOUND_LABEL:
									Width: PARENT_WIDTH
									Height: 20
									Text: label-sound-volume-container
								ExponentialSlider@SOUND_VOLUME:
									Y: 30
									Width: PARENT_WIDTH
									Height: 20
									Ticks: 7
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@MUTE_BACKGROUND_MUSIC_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Checkbox@MUTE_BACKGROUND_MUSIC:
									Width: PARENT_WIDTH
									Height: 20
									Font: Regular
									Text: checkbox-mute-background-music-container.label
									TooltipText: checkbox-mute-background-music-container.tooltip
									TooltipContainer: SETTINGS_TOOLTIP_CONTAINER
						Container@MUSIC_VOLUME_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@MUSIC_LABEL:
									Width: PARENT_WIDTH
									Height: 20
									Text: label-music-title-volume-container
								ExponentialSlider@MUSIC_VOLUME:
									Y: 25
									Width: PARENT_WIDTH
									Height: 20
									Ticks: 7
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 50
					Children:
						Container@AUDIO_DEVICE_CONTAINER:
							X: 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@AUDIO_DEVICE_LABEL:
									Width: PARENT_WIDTH
									Height: 20
									Text: label-audio-device-container
								DropDownButton@AUDIO_DEVICE:
									Y: 25
									Width: PARENT_WIDTH
									Height: 25
						Container@VIDEO_VOLUME_CONTAINER:
							X: PARENT_WIDTH / 2 + 10
							Width: PARENT_WIDTH / 2 - 20
							Children:
								Label@VIDEO_LABEL:
									Width: PARENT_WIDTH
									Height: 20
									Text: label-video-volume-container
								ExponentialSlider@VIDEO_VOLUME:
									Y: 25
									Width: PARENT_WIDTH
									Height: 20
									Ticks: 7
				Container@ROW:
					Width: PARENT_WIDTH - 24
					Height: 20
					Children:
						Container@RESTART_REQUIRED_CONTAINER:
							X: 10
							Width: PARENT_WIDTH - 10
							Children:
								Label@AUDIO_RESTART_REQUIRED_DESC:
									Width: PARENT_WIDTH
									Height: 20
									Font: Tiny
									Align: Center
									Text: label-restart-required-container-audio-desc
