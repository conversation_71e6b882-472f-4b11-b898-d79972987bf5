{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\OpenRA.Platforms.Default.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj", "projectName": "OpenRA.Game", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Linguini.Bundle": {"target": "Package", "version": "[0.8.1, )"}, "Microsoft.Extensions.DependencyModel": {"target": "Package", "version": "[6.0.2, )"}, "Mono.NAT": {"target": "Package", "version": "[3.0.4, )"}, "OpenRA-Eluant": {"target": "Package", "version": "[1.0.22, )"}, "Roslynator.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "Roslynator.Formatting.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "SharpZipLib": {"target": "Package", "version": "[1.4.2, )"}, "StyleCop.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[1.2.0-beta.435, )"}, "System.Runtime.Loader": {"target": "Package", "version": "[4.3.0, )"}, "System.Threading.Channels": {"target": "Package", "version": "[6.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\OpenRA.Platforms.Default.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\OpenRA.Platforms.Default.csproj", "projectName": "OpenRA.Platforms.Default", "projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\OpenRA.Platforms.Default.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Platforms.Default\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\GitHub\\cnc-ca-ai\\CAmod\\engine\\OpenRA.Game\\OpenRA.Game.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"OpenRA-Freetype6": {"target": "Package", "version": "[1.0.11, )"}, "OpenRA-OpenAL-CS": {"target": "Package", "version": "[1.0.22, )"}, "OpenRA-SDL2-CS": {"target": "Package", "version": "[1.0.42, )"}, "Roslynator.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "Roslynator.Formatting.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[4.2.0, )"}, "StyleCop.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[1.2.0-beta.435, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}