mcv:
	idle:
		Filename: mcv.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mcvicnh.tem

mcv.destroyed:
	idle:
		Filename: mcv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

harv:
	Defaults:
		Filename: harv.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	harvest:
		Start: 32
		Length: 4
		Facings: 8
		Tick: 60
	dock:
		Filename: harvdump.shp
		Length: 7
	dock-loop:
		Filename: harvdump.shp
		Start: 7
	icon:
		Filename: harvicnh.tem

harv.destroyed:
	idle:
		Filename: harv.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

bggy:
	Defaults:
		Filename: bggy.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	icon:
		Filename: bggyicnh.tem

bggy.destroyed:
	idle:
		Filename: bggy.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: bggy.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mtnk:
	Defaults:
		Filename: mtnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: *
	icon:
		Filename: mtnkicnh.tem

mtnk.destroyed:
	idle:
		Filename: mtnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: mtnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ltnk:
	Defaults:
		Filename: ltnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: *
	icon:
		Filename: ltnkicnh.tem

ltnk.destroyed:
	idle:
		Filename: ltnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: ltnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

htnk:
	Defaults:
		Filename: htnk.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: *
	icon:
		Filename: htnkicnh.tem

htnk.destroyed:
	idle:
		Filename: htnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: htnk.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

jeep:
	Defaults:
		Filename: jeep.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	icon:
		Filename: jeepicnh.tem

jeep.destroyed:
	idle:
		Filename: jeep.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: jeep.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

bike:
	idle:
		Filename: bike.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: bikeicnh.tem

bike.destroyed:
	idle:
		Filename: bike.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

ftnk:
	idle:
		Filename: ftnk.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Combine:
			0:
				Filename: flame-n.shp
				Length: *
				Offset: 3,6
			1:
				Filename: flame-nw.shp
				Length: *
				Offset: 8,7
			2:
				Filename: flame-w.shp
				Length: *
				Offset: 8,2
			3:
				Filename: flame-sw.shp
				Length: *
				Offset: 7,-2
			4:
				Filename: flame-s.shp
				Length: *
				Offset: 3,-2
			5:
				Filename: flame-se.shp
				Length: *
				Offset: -5,-2
			6:
				Filename: flame-e.shp
				Length: *
				Offset: -7,2
			7:
				Filename: flame-ne.shp
				Length: *
				Offset: -7,8
		Facings: 8
		Length: 13
	icon:
		Filename: ftnkicnh.tem

ftnk.destroyed:
	idle:
		Filename: ftnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mhq:
	Defaults:
		Filename: mhq.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	spinner:
		Start: 32
		Length: 32
	icon:
		Filename: mhqicnh.shp

msam:
	Defaults:
		Filename: msam.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	empty-aim:
		Start: 64
		Facings: 32
		UseClassicFacings: True
	aim:
		Start: 64
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: msamicnh.tem

msam.destroyed:
	idle:
		Filename: msam.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: msam.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

mlrs:
	Defaults:
		Filename: mlrs.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Start: 32
		Facings: 32
		UseClassicFacings: True
	turret1:
		Start: 64
		Facings: 32
		UseClassicFacings: True
	turret0:
		Start: 96
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: mlrsicnh.tem

mlrs.destroyed:
	idle:
		Filename: mlrs.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: mlrs.shp
		Start: 32
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

stnk:
	idle:
		Filename: stnk.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: stnkicnh.tem

stnk.destroyed:
	idle:
		Filename: stnk.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

arty:
	idle:
		Filename: arty.shp
		Facings: 32
		UseClassicFacings: True
	muzzle:
		Filename: gunfire2.shp
		Length: *
	icon:
		Filename: artyicnh.tem

arty.destroyed:
	idle:
		Filename: arty.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512

apc:
	Defaults:
		Filename: apc.shp
	idle:
		Facings: 32
		UseClassicFacings: True
	turret:
		Filename: apctur.shp
		Facings: 32
	turret-air:
		Filename: apctur.shp
		Start: 32
		Facings: 32
	muzzle:
		Filename: minigun.shp
		Length: 6
		Facings: 8
	muzzle-air:
		Filename: apcmuz.shp
		Length: 3
		Stride: 6
		Facings: 8
	close:
		Start: 32
		Length: 3
	unload:
		Start: 32
	icon:
		Filename: apcicnh.tem

apc.destroyed:
	idle:
		Filename: apc.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
	turret:
		Filename: apctur.shp
		Facings: 32
		ZOffset: -512

truck:
	idle:
		Filename: truck.shp
		Facings: 32
		UseClassicFacings: True
	icon:
		Filename: truckicon.shp

truck.destroyed:
	idle:
		Filename: truck.shp
		Facings: 32
		UseClassicFacings: True
		ZOffset: -512
