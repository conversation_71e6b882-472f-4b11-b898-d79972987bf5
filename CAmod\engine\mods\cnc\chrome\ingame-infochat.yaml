Container@CHAT_CONTAINER:
	X: 15
	Y: 15
	Width: PARENT_WIDTH - 30
	Height: PARENT_HEIGHT - 30
	Logic: IngameChatLogic
		Templates:
			Chat: CHAT_LINE_TEMPLATE
			System: SYSTEM_LINE_TEMPLATE
			Mission: CHAT_LINE_TEMPLATE
	Children:
		Container@CHAT_CHROME:
			Width: PARENT_WIDTH
			Height: PARENT_HEIGHT
			Children:
				Button@CHAT_MODE:
					Y: PARENT_HEIGHT - HEIGHT
					Width: 50
					Height: 25
					Text: button-chat-chrome-mode.label
					Font: Bold
					Key: ToggleChatMode
					TooltipText: button-chat-chrome-mode.tooltip
					TooltipContainer: TOOLTIP_CONTAINER
				TextField@CHAT_TEXTFIELD:
					X: 55
					Y: PARENT_HEIGHT - HEIGHT
					Width: PARENT_WIDTH - 55
					Height: 25
				ScrollPanel@CHAT_SCROLLPANEL:
					Width: PARENT_WIDTH
					Height: PARENT_HEIGHT - 30
					TopBottomSpacing: 3
					ItemSpacing: 2
