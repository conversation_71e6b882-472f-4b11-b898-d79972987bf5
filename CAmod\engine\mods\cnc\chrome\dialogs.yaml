ScrollPanel@LABEL_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@HEADER:
			Background: scrollheader
			Width: PARENT_WIDTH - 27
			Height: 13
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					Font: TinyBold
					Width: PARENT_WIDTH
					Height: 13
					Align: Center
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					X: 10
					Width: PARENT_WIDTH - 20
					Height: 25

ScrollPanel@PLAYERACTION_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					X: 10
					Width: PARENT_WIDTH - 20
					Height: 25
					Align: Left

ScrollPanel@FACTION_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@HEADER:
			Background: scrollheader
			Width: PARENT_WIDTH - 27
			Height: 13
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					Font: TinyBold
					Width: PARENT_WIDTH
					Height: 13
					Align: Center
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			TooltipContainer: TOOLTIP_CONTAINER
			Children:
				Image@FLAG:
					X: 4
					Y: 4
					Width: 32
					Height: 16
				Label@LABEL:
					X: 40
					Width: 50
					Height: 25

ScrollPanel@TEAM_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					X: 0
					Width: PARENT_WIDTH
					Height: 25
					Align: Center

ScrollPanel@SPAWN_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					X: 0
					Width: PARENT_WIDTH
					Height: 25
					Align: Center

ScrollPanel@SPECTATOR_DROPDOWN_TEMPLATE:
	Width: DROPDOWN_WIDTH
	Background: panel-black
	Children:
		ScrollItem@HEADER:
			Background: scrollheader
			Width: PARENT_WIDTH - 27
			Height: 13
			X: 2
			Y: 0
			Visible: false
			Children:
				Label@LABEL:
					Font: TinyBold
					Width: PARENT_WIDTH
					Height: 13
					Align: Center
		ScrollItem@TEMPLATE:
			Width: PARENT_WIDTH - 27
			Height: 25
			X: 2
			Y: 0
			Visible: false
			Children:
				Image@FLAG:
					X: 4
					Y: 4
					Width: 32
					Height: 16
				Label@LABEL:
					X: 40
					Width: PARENT_WIDTH
					Height: 25
					Shadow: True
				Label@NOFLAG_LABEL:
					X: 5
					Width: PARENT_WIDTH
					Height: 25
					Shadow: True

Background@THREEBUTTON_PROMPT:
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - 90) / 2
	Width: 500
	Height: 41
	Background: panel-black
	Children:
		Label@PROMPT_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Label@PROMPT_TEXT:
			Y: 22
			Width: PARENT_WIDTH
			Height: 20
			Font: Bold
			Align: Center
		Button@CONFIRM_BUTTON:
			Key: return
			X: 360
			Y: 40
			Width: 140
			Height: 35
			Text: button-prompt-confirm
			Visible: false
		Button@OTHER_BUTTON:
			Key: r
			X: 210
			Y: 40
			Width: 140
			Height: 35
			Text: button-prompt-other
			Visible: false
		Button@CANCEL_BUTTON:
			Key: escape
			Y: 40
			Width: 140
			Height: 35
			Text: button-cancel
			Visible: false

Background@TWOBUTTON_PROMPT:
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - 90) / 2
	Width: 370
	Height: 31
	Background: panel-black
	Children:
		Label@PROMPT_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Label@PROMPT_TEXT:
			Y: 17
			Width: PARENT_WIDTH
			Height: 20
			Font: Bold
			Align: Center
		Button@CANCEL_BUTTON:
			Key: escape
			Y: 30
			Width: 140
			Height: 35
			Text: button-cancel
			Visible: false
		Button@CONFIRM_BUTTON:
			Key: return
			X: 230
			Y: 30
			Width: 140
			Height: 35
			Text: button-prompt-confirm
			Visible: false

Container@TEXT_INPUT_PROMPT:
	X: (WINDOW_WIDTH - WIDTH) / 2
	Y: (WINDOW_HEIGHT - HEIGHT) / 2
	Width: 370
	Height: 80
	Children:
		Label@PROMPT_TITLE:
			Width: PARENT_WIDTH
			Height: 25
			Y: 0 - 34
			Font: BigBold
			Contrast: true
			Align: Center
		Background@bg:
			Width: PARENT_WIDTH
			Height: 80
			Background: panel-black
			Children:
				Label@PROMPT_TEXT:
					X: 20
					Y: 12
					Width: PARENT_WIDTH - 40
					Height: 25
					Font: Bold
					Align: Center
				TextField@INPUT_TEXT:
					X: 20
					Y: 40
					Width: PARENT_WIDTH - 40
					Height: 25
		Button@ACCEPT_BUTTON:
			X: PARENT_WIDTH - 160
			Y: PARENT_HEIGHT - 1
			Width: 160
			Height: 34
			Text: button-text-input-prompt-accept
			Font: Bold
			Key: return
		Button@CANCEL_BUTTON:
			X: 0
			Y: PARENT_HEIGHT - 1
			Width: 160
			Height: 35
			Text: button-cancel
			Font: Bold
			Key: escape

ScrollPanel@NEWS_PANEL:
	Width: 400
	Height: 265
	Background: panel-black
	TopBottomSpacing: 10
	ItemSpacing: 5
	Children:
		Container@NEWS_ITEM_TEMPLATE:
			X: 10
			Width: PARENT_WIDTH - 40
			Height: 40
			Children:
				Label@TITLE:
					Width: PARENT_WIDTH
					Height: 20
					Align: Center
					Font: Bold
				Label@AUTHOR_DATETIME:
					Y: 21
					Width: PARENT_WIDTH
					Height: 15
					Align: Center
					Font: TinyBold
				Label@CONTENT:
					Y: 40
					Width: PARENT_WIDTH
		Label@NEWS_STATUS:
			X: 80
			Y: 1
			Width: PARENT_WIDTH - 80 - 80 - 24
			Height: PARENT_HEIGHT
			Align: Center
			VAlign: Middle
