MCV:
	Inherits: ^Vehicle
	Inherits@selection: ^SelectableSupportUnit
	Valued:
		Cost: 3000
	Tooltip:
		Name: actor-mcv.name
	Buildable:
		BuildPaletteOrder: 100
		Queue: Vehicle.GDI, Vehicle.Nod
		Description: actor-mcv.description
	Selectable:
		DecorationBounds: 1536, 1536
	Mobile:
		Speed: 60
		Locomotor: heavywheeled
	Health:
		HP: 120000
	Encyclopedia:
		Description: actor-mcv.encyclopedia
		Order: 80
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	Repairable:
		HpPerStep: 1819
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 8c0
	Transforms:
		IntoActor: fact
		Offset: -1,-1
		Facing: 432
		TransformSounds: constru2.aud, hvydoor1.aud
		NoTransformNotification: BuildingCannotPlaceAudio
		NoTransformTextNotification: notification-cannot-deploy-here
		Voice: Unload
	MustBeDestroyed:
		RequiredForShortGame: true
	BaseBuilding:
	SpawnActorOnDeath:
		Actor: MCV.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

HARV:
	Inherits: ^Tank
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@selection: ^SelectableEconomicUnit
	Valued:
		Cost: 1100
	Tooltip:
		Name: actor-harv.name
		GenericName: actor-harv.generic-name
	Buildable:
		BuildPaletteOrder: 10
		Prerequisites: proc
		Queue: Vehicle.GDI, Vehicle.Nod
		Description: actor-harv.description
	Selectable:
		DecorationBounds: 1536, 1536
	Harvester:
		Resources: Tiberium, BlueTiberium
		BaleLoadDelay: 12
		BaleUnloadDelay: 6
		SearchFromProcRadius: 15
		SearchFromHarvesterRadius: 8
		HarvestFacings: 8
		EmptyCondition: no-tiberium
	StoresResources:
		Capacity: 20
		Resources: Tiberium, BlueTiberium
	DockClientManager:
	Mobile:
		Speed: 72
	Health:
		HP: 62500
	Encyclopedia:
		Description: actor-harv.encyclopedia
		Order: 90
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	Repairable:
		HpPerStep: 2584
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 4c0
	ActorLostNotification:
		Notification: HarvesterLost
		TextNotification: notification-harvester-lost
	SpawnActorOnDeath:
		Actor: HARV.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	WithHarvestAnimation:
	WithDockingAnimation:
	FireWarheadsOnDeath@EMPTY:
		Weapon: UnitExplodeHarvEmpty
		EmptyWeapon: UnitExplodeHarvEmpty
	FireWarheadsOnDeath:
		RequiresCondition: !no-tiberium
		Weapon: TiberiumExplosion
	WithStoresResourcesPipsDecoration:
		Position: BottomLeft
		Margin: 4, 3
		RequiresSelection: true
		PipCount: 7
		ResourceSequences:
			Tiberium: pip-green
			BlueTiberium: pip-blue

APC:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Inherits@TRANSPORT: ^Transport
	Valued:
		Cost: 600
	Tooltip:
		Name: actor-apc.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: pyle
		Queue: Vehicle.GDI
		Description: actor-apc.description
	Mobile:
		TurnSpeed: 20
		Speed: 128
		PauseOnCondition: notmobile
	Health:
		HP: 19000
	Encyclopedia:
		Description: actor-apc.encyclopedia
		Order: 100
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	Repairable:
		HpPerStep: 1440
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 7c0
	Turreted:
		TurnSpeed: 40
	AutoTargetPriority@Air:
		ValidTargets: Air
		Priority: 2
	Armament@PRIMARY:
		Weapon: APCGun
		Recoil: 48
		RecoilRecovery: 18
		LocalOffset: 255,100,189, 255,-100,189
		MuzzleSequence: muzzle
		PauseOnCondition: reload-air
		ReloadingCondition: reload-ground
	Armament@SECONDARY:
		Name: secondary
		Weapon: APCGun.AA
		Recoil: 48
		RecoilRecovery: 18
		LocalOffset: 175,100,299, 175,-100,299
		MuzzleSequence: muzzle-air
		PauseOnCondition: reload-ground
		ReloadingCondition: reload-air
	GrantConditionOnAttack:
		Condition: attack-air
		ArmamentNames: secondary
		RevokeDelay: 18
		RevokeOnNewTarget: False
		RevokeAll: True
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
		RequiresCondition: !attack-air
	WithSpriteTurret@AIR:
		Sequence: turret-air
		RequiresCondition: attack-air
	Cargo:
		Types: Infantry
		MaxWeight: 5
		UnloadVoice: Unload
		LoadingCondition: notmobile
	FireWarheadsOnDeath:
		Weapon: UnitExplodeBig
		EmptyWeapon: UnitExplodeBig
	SpawnActorOnDeath:
		Actor: APC.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

ARTY:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 600
	Tooltip:
		Name: actor-arty.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.Nod
		Description: actor-arty.description
	Mobile:
		TurnSpeed: 16
		Speed: 72
	Health:
		HP: 7500
	Repairable:
		HpPerStep: 568
	Armor:
		Type: Light
	RevealsShroud:
		Range: 5c0
	Armament:
		Weapon: ArtilleryShell
		LocalOffset: 624,0,208
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-arty.encyclopedia
		Order: 110
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	AutoTarget:
		InitialStanceAI: Defend
	SpawnActorOnDeath:
		Actor: ARTY.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	FireWarheadsOnDeath:
		Weapon: ArtilleryShell
		EmptyWeapon: ArtilleryShell

FTNK:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 600
	Tooltip:
		Name: actor-ftnk.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 50
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.Nod
		Description: actor-ftnk.description
	Mobile:
		TurnSpeed: 28
		Speed: 92
	Health:
		HP: 27000
	Repairable:
		HpPerStep: 2046
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 6c0
	Armament:
		Weapon: BigFlamer
		LocalOffset: 512,128,42, 512,-128,42
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-ftnk.encyclopedia
		Order: 120
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	WithMuzzleOverlay:
	FireWarheadsOnDeath:
		Weapon: FlametankExplode
		EmptyWeapon: FlametankExplode
	SpawnActorOnDeath:
		Actor: FTNK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

BGGY:
	Inherits: ^Vehicle
	Inherits@@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 300
	Tooltip:
		Name: actor-bggy.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: afld
		Queue: Vehicle.Nod
		Description: actor-bggy.description
	Mobile:
		TurnSpeed: 40
		Speed: 170
	Health:
		HP: 12000
	Repairable:
		HpPerStep: 1819
	Armor:
		Type: Light
	RevealsShroud:
		Range: 8c0
	Turreted:
		TurnSpeed: 40
		Offset: -43,0,128
	Armament:
		Weapon: MachineGun
		LocalOffset: 171,0,43
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-bggy.encyclopedia
		Order: 130
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: BGGY.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

BIKE:
	Inherits: ^Vehicle
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Valued:
		Cost: 500
	Tooltip:
		Name: actor-bike.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 30
		Prerequisites: afld
		Queue: Vehicle.Nod
		Description: actor-bike.description
	Mobile:
		TurnSpeed: 40
		Speed: 192
		Locomotor: wheeled
	Health:
		HP: 11000
	Repairable:
		HpPerStep: 1000
	Armor:
		Type: Light
	RevealsShroud:
		Range: 8c0
	Armament:
		Weapon: BikeRockets
		LocalOffset: -128, -170, 170, -128, 170, 170
		LocalYaw: 100, -100
	Encyclopedia:
		Description: actor-bike.encyclopedia
		Order: 140
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	SpawnActorOnDeath:
		Actor: BIKE.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

JEEP:
	Inherits: ^Vehicle
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 400
	Tooltip:
		Name: actor-jeep.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 20
		Prerequisites: weap
		Queue: Vehicle.GDI
		Description: actor-jeep.description
	Mobile:
		TurnSpeed: 40
		Speed: 145
	Health:
		HP: 16000
	Repairable:
		HpPerStep: 1819
	Armor:
		Type: Light
	RevealsShroud:
		Range: 8c0
	Turreted:
		TurnSpeed: 40
		Offset: -85,0,128
	Armament:
		Weapon: MachineGunH
		LocalOffset: 171,0,85
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-jeep.encyclopedia
		Order: 150
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: JEEP.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

LTNK:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 750
	Tooltip:
		Name: actor-ltnk.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.Nod
		Description: actor-ltnk.description
	Mobile:
		TurnSpeed: 28
		Speed: 102
	Health:
		HP: 32000
	Repairable:
		HpPerStep: 2062
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 6c0
	Turreted:
		TurnSpeed: 28
	Armament:
		Weapon: 70mm
		Recoil: 85
		RecoilRecovery: 17
		LocalOffset: 720,0,90
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-ltnk.encyclopedia
		Order: 160
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	SpawnActorOnDeath:
		Actor: LTNK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true

MTNK:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 900
	Tooltip:
		Name: actor-mtnk.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 40
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.GDI
		Description: actor-mtnk.description
	Mobile:
		Speed: 72
	Health:
		HP: 45000
	Repairable:
		HpPerStep: 2274
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 6c0
	Turreted:
		TurnSpeed: 20
	Armament:
		Weapon: 120mm
		Recoil: 128
		RecoilRecovery: 26
		LocalOffset: 768,0,90
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-mtnk.encyclopedia
		Order: 170
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	WithSpriteTurret:
	FireWarheadsOnDeath:
		Weapon: UnitExplodeBig
		EmptyWeapon: UnitExplodeBig
	SpawnActorOnDeath:
		Actor: MTNK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	Selectable:
		DecorationBounds: 1194, 1194

HTNK:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Valued:
		Cost: 1800
	Tooltip:
		Name: actor-htnk.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 60
		Prerequisites: eye, ~techlevel.high
		Queue: Vehicle.GDI
		Description: actor-htnk.description
	Mobile:
		Locomotor: heavytracked
		Speed: 46
		TurnSpeed: 14
	Health:
		HP: 87000
	Repairable:
		HpPerStep: 2198
	Armor:
		Type: Heavy
	RevealsShroud:
		Range: 6c0
	WithSpriteTurret:
	Turreted:
		TurnSpeed: 14
	Armament@PRIMARY:
		Weapon: 120mmDual
		LocalOffset: 900,180,340, 900,-180,340
		Recoil: 170
		RecoilRecovery: 42
		MuzzleSequence: muzzle
	Armament@SECONDARY:
		Name: secondary
		Weapon: MammothMissiles
		LocalOffset: -85,384,340, -85,-384,340
		LocalYaw: -100, 100
		Recoil: 10
		MuzzleSequence: muzzle
	Encyclopedia:
		Description: actor-htnk.encyclopedia
		Order: 180
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	AttackMove:
		Voice: Attack
	AttackTurreted:
		Voice: Attack
	WithMuzzleOverlay:
	ChangesHealth:
		Step: 500
		Delay: 10
		StartIfBelow: 50
		DamageCooldown: 200
	FireWarheadsOnDeath:
		Weapon: UnitExplodeMech
		EmptyWeapon: UnitExplodeMech
	SpawnActorOnDeath:
		Actor: HTNK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	Selectable:
		DecorationBounds: 1450, 1450, 0, -128

MSAM:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetGroundAssaultMove
	Valued:
		Cost: 900
	Tooltip:
		Name: actor-msam.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 50
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.GDI
		Description: actor-msam.description
	Mobile:
		Speed: 72
		TurnSpeed: 16
	Health:
		HP: 12000
	Repairable:
		HpPerStep: 606
	Armor:
		Type: Light
	RevealsShroud:
		Range: 6c0
	Turreted:
		TurnSpeed: 512
		Offset: -256,0,128
	Armament:
		Weapon: 227mm
		LocalOffset: 213,128,0, 213,-128,0
	Armament@SECONDARY:
		Name: secondary
		Weapon: 227mm
		LocalOffset: 213,-128,0, 213,128,0
	Encyclopedia:
		Description: actor-msam.encyclopedia
		Order: 190
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	AttackFrontal:
		TargetFrozenActors: True
		ForceFireIgnoresActors: True
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	WithSpriteTurret:
	FireWarheadsOnDeath:
		Weapon: UnitExplodeMech
		EmptyWeapon: UnitExplodeMech
	SpawnActorOnDeath:
		Actor: MSAM.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	WithTurretAimAnimation:
		Sequence: aim

MLRS:
	Inherits: ^Tank
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@CLOAK: ^AcceptsCloakCrate
	Inherits@AUTOTARGET: ^AutoTargetAir
	Valued:
		Cost: 600
	Tooltip:
		Name: actor-mlrs.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 70
		Prerequisites: anyhq, ~techlevel.medium
		Queue: Vehicle.Nod
		Description: actor-mlrs.description
	Mobile:
		Speed: 92
		TurnSpeed: 28
	Health:
		HP: 18000
	Repairable:
		HpPerStep: 1364
	Armor:
		Type: Light
	RevealsShroud:
		Range: 8c0
	Turreted:
		TurnSpeed: 32
		Offset: -128,0,128
		RealignDelay: 0
	Armament:
		Weapon: Patriot
		LocalOffset: 0,-171,0, 0,171,0
	Encyclopedia:
		Description: actor-mlrs.encyclopedia
		Order: 200
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AmmoPool:
		Ammo: 2
		AmmoCondition: ammo
	AttackTurreted:
	WithSpriteTurret:
		RequiresCondition: ammo > 1
	WithSpriteTurret@OneMissile:
		RequiresCondition: ammo == 1
		Sequence: turret1
	WithSpriteTurret@NoMissiles:
		RequiresCondition: !ammo
		Sequence: turret0
	AutoTarget:
		InitialStanceAI: Defend
	RenderRangeCircle:
	SpawnActorOnDeath:
		Actor: MLRS.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	ReloadAmmoPool:
		Delay: 45
		Count: 1

STNK:
	Inherits: ^Vehicle
	Inherits@EXPERIENCE: ^GainsExperience
	Inherits@AUTOTARGET: ^AutoTargetAllAssaultMove
	Valued:
		Cost: 900
	Tooltip:
		Name: actor-stnk.name
	UpdatesPlayerStatistics:
		AddToArmyValue: true
	Buildable:
		BuildPaletteOrder: 90
		Prerequisites: tmpl, ~techlevel.high
		Queue: Vehicle.Nod
		Description: actor-stnk.description
	Mobile:
		Locomotor: heavywheeled
		TurnSpeed: 40
		Speed: 127
	Targetable:
		TargetTypes: Ground, Vehicle, StealthTank
	Health:
		HP: 15000
	Repairable:
		HpPerStep: 758
	Armor:
		Type: Light
	RevealsShroud:
		Range: 7c0
	Cloak:
		InitialDelay: 85
		CloakDelay: 85
		CloakSound: trans1.aud
		UncloakSound: trans1.aud
		CloakStyle: Palette
		CloakedPalette: cloak
		UncloakOn: Attack, Unload, Dock, Damage, Heal
		PauseOnCondition: cloak-force-disabled
	GrantConditionOnDamageState@UNCLOAK:
		Condition: cloak-force-disabled
		ValidDamageStates: Critical
	Armament@PRIMARY:
		Weapon: 227mm.stnk
		LocalOffset: 213,43,128, 213,-43,128
	Armament@SECONDARY:
		Weapon: 227mm.stnkAA
		LocalOffset: 213,43,128, 213,-43,128
	Encyclopedia:
		Description: actor-stnk.encyclopedia
		Order: 210
		Category: Vehicles
		Scale: 3
		PreviewOwner: NodUnits
	AttackFrontal:
		Voice: Attack
		FacingTolerance: 0
	AttackMove:
		Voice: Attack
	AutoTarget:
		InitialStance: HoldFire
		InitialStanceAI: ReturnFire
	FireWarheadsOnDeath:
		Weapon: UnitExplodeStealthTank
		EmptyWeapon: UnitExplodeStealthTank
	SpawnActorOnDeath:
		Actor: STNK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
	-MustBeDestroyed:

MHQ:
	Inherits: ^Vehicle
	Valued:
		Cost: 1000
	Tooltip:
		Name: actor-mhq.name
	Health:
		HP: 20000
	Armor:
		Type: Light
	Mobile:
		Speed: 72
	RevealsShroud:
		Range: 6c0
	WithIdleOverlay@SPINNER:
		Sequence: spinner
		Offset: -256,0,256
	Buildable:
		Description: actor-mhq.description

TRUCK:
	Inherits: ^Vehicle
	Inherits@selection: ^SelectableSupportUnit
	Buildable:
		Queue: Vehicle.GDI, Vehicle.Nod
		BuildPaletteOrder: 35
		Prerequisites: vehicleproduction
		BuildDuration: 500
		Description: actor-truck.description
	Valued:
		Cost: 1000
	Tooltip:
		Name: actor-truck.name
	Health:
		HP: 11000
	Armor:
		Type: Light
	Mobile:
		Speed: 113
	RevealsShroud:
		Range: 4c0
	Encyclopedia:
		Description: actor-truck.encyclopedia
		Order: 220
		Category: Vehicles
		Scale: 3
		PreviewOwner: GDI
	DeliversCash:
		Payload: 1000
		PlayerExperience: 10
	SpawnActorOnDeath:
		Actor: TRUCK.Husk
		OwnerType: InternalName
		EffectiveOwnerFromOwner: true
